<?php

namespace App\Listeners;

use App\Events\AccountCreatedEvent;
use App\Models\Account;
use App\Models\SubscriptionPlan;

class AccountCreatedListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(AccountCreatedEvent $event): void
    {
        $account = $event->account;
        $subscriptionPlan = SubscriptionPlan::first();
        $this->subscribeAccountToPlan($account, $subscriptionPlan);
    }

    private function subscribeAccountToPlan(Account $account, SubscriptionPlan $subscriptionPlan)
    {
        // Update the account's subscription plan
        $account->update([
            'subscription_plan_id' => $subscriptionPlan->id
        ]);

        // Create the subscription
        $account->subscriptions()->create([
            'subscription_plan_id' => $subscriptionPlan->id,
            'start_date' => now(),
        ]);
    }
}
