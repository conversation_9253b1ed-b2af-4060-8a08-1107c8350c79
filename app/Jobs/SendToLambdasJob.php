<?php

namespace App\Jobs;

use App\Enums\ResponseStatusEnum;
use App\Enums\StatusEnum;
use App\Enums\EndpointEnum;
use App\Enums\SubmissionStatusEnum;
use App\Models\RequestLog;
use App\Models\ResponseLog;
use App\Models\Submission;
use App\Models\SubmissionLog;
use App\Services\ApiService;
use Aws\Lambda\LambdaClient;
use App\Models\CipcCustomer;
use Exception;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;

use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\StreamInterface;
use Symfony\Component\HttpFoundation\Response;


class SendToLambdasJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $timeout = 900;
    protected Submission $submission;
    protected array|null $requestData;
    protected LambdaClient $client;

    public function __construct(Submission $submission)
    {
        $this->submission = $submission;
        $this->onQueue('lambdas');
    }

    /**
     * Define a unique identifier for the job.
     */
    public function uniqueId()
    {
        return $this->submission->id;
    }

    /**
     * Execute the job.
     */
    public function handle(LambdaClient $client): void
    {
        $this->client = $client;
        if (in_array($this->submission->endpoint_id, EndpointEnum::getSDKTransactionTypes())) {
            $this->triggerBotViaSDK(false);
        } elseif (config('services.submissions.bots_method') === 'http') {
            $this->triggerBotViaHTTP();
        } else {
            $this->triggerBotViaSDK(false);
        }
    }

    public function buildApiClient($httpClient, $curlLogger)
    {
        $apiBaseUrl = rtrim(config('services.submissions.api_base_url'), '/');
        $apiKey = config('services.submissions.api_key');
        $headers = ['x-api-key' => $apiKey];

        //        $service = new ApiService();
    }

    public function handleSuccess($json): void
    {
        // TEMP!!!
        echo "SendToLambdasJob::handleSuccess" . PHP_EOL;

        try {
            $json = json_encode($json);
            $this->createResponseLog(
                responseStatusEnum: ResponseStatusEnum::DONE,
                cipcStatus: StatusEnum::CIPC_PENDING,
                responseJson: $json
            );
        } catch (\Exception $e) {
            //Handle Error on our side
            // ToDo: Send to slack
            Log::error('Error occurred while handling success', ['error' => $e->getMessage()]);
        }
    }

    public function handleError($e): void
    {
        // TEMP!!!
        echo "SendToLambdasJob::handleError" . PHP_EOL;

        $status = StatusEnum::INFO_SUPPORT;
        $json = json_encode([
            'status' => 'failed',
            'message' => $e->getMessage(),
            'response' => __('errors.internal_server_error')
        ]);
        $responseStatus = ResponseStatusEnum::ERROR;
        $responseLog = $this->createResponseLog(
            responseStatusEnum: $responseStatus,
            cipcStatus: $status,
            responseJson: $json
        );
        //event(new SubmissionFailedEvent($responseLog));
    }

    public function updateRequestLog(RequestLog $requestLog, StatusEnum $status, $responseJson): void
    {
        $requestLog->update([
            'status_id' => $status,
            'response_body' => $responseJson,
        ]);
    }

    public function createResponseLog(
        ResponseStatusEnum $responseStatusEnum,
        StatusEnum         $cipcStatus,
        $responseJson
    ): ResponseLog {
        return ResponseLog::create([
            'submission_id' => $this->submission->id,
            'status_code' => $responseStatusEnum, //HTTP response
            'cipc_status' => $cipcStatus, //CIPC transaction
            'response_body' => $responseJson,
            'result' => '', //ToDo: confirm what this is for
        ]);
    }

    public function handleInternalServerErrors($body): void //ToDo: Investigate why this would happen fix
    {

        if (isset($body['message']) && $body['message'] === 'Internal server error') {
            //put back in queue
            $this->submission->update(['status' => SubmissionStatusEnum::PENDING->value]);
            Log::error('Internal server error from http method');
        }
    }

    public function buildPayload(): array
    {
        $data = $this->submission->json_data;
        if (!is_array($data)) {
            $data = json_decode($data, true);
        }
        if (!isset($data['customer_code'])) {
            $data['customer_code'] = null;
        }
        if (!isset($data['customer_password'])) {
            $data['customer_password'] = null;
        }
        $query_data = $data;
        if (isset($data['query_data'])) {
            $query_data = $data['query_data'];
        }
        // TODO: Remove backward compatibility
        $payload = [
            'Transaction_Id' => $this->submission->reference,  //todo: send id instead of reference
            'Customer_Code' => $data['customer_code'],
            'Customer_Password' => $data['customer_password'],
            'Query_Type' => EndpointEnum::getNameFromId($this->submission->endpoint_id),
            'Query_Data' => $query_data,
            'Slack_TS' => empty($data['slack_ts']) ? null : $data['slack_ts'],
        ];
        $auth_url = CipcCustomer::where('code', $data['customer_code'])
            ->value('auth_url');
        if (!empty($auth_url)) {
            $payload['Auth_Url'] = $auth_url;
        }
        //handle BO Filing differently
        if ($this->submission->endpoint_id === EndpointEnum::BENEFICIAL_OWNERSHIP_FILING->value && !empty($data['Otps'])) {
            $payload['Otps'] = $data['Otps'];
            $payload['Query_Type'] = 'BeneficialOwnershipVerify';
        }

        if ($this->submission->endpoint_id === EndpointEnum::DIRECTOR_CHANGE->value) {
            // Check for verify request data structure (directors with OTPs at root level)
            if (isset($data['directors']) && is_array($data['directors'])) {
                $directors = $data['directors'];
                if (array_filter($directors, fn($director) => isset($director['otps']))) {
                    $payload['Query_Type'] = 'DirectorChangeVerify';
                }
            } else {
                // Check for verify request data structure (directors with OTPs at query_data level)
                $directors = $data['query_data']['directors'];
                if (array_filter($directors, fn($director) => isset($director['otps']))) {
                    $payload['Query_Type'] = 'DirectorChangeVerify';
                }
            }
        }

        //handle annual returns differently
        if ($this->submission->endpoint_id === EndpointEnum::ANNUAL_RETURN->value) {
            if ($data['query_data']['type'] === 'afs') {
                $payload['Query_Type'] = 'AnnualReturnAfs';
            } elseif ($data['query_data']['type'] === 'fas') {
                $payload['Query_Type'] = 'AnnualReturnFas';
            }
        }
        return $payload;
    }

    public function triggerBotViaSDK($async = true): void
    {
        $payload = [
            'body' => $this->buildPayload()
        ];
        $functionName = 'eServices';
        if (in_array($this->submission->endpoint_id, EndpointEnum::getEServicesClassicTransactions())) {
            $functionName = 'eServicesClassic';
        }
        //create submission log
        $submissionLog = SubmissionLog::create([
            'submission_id' => $this->submission->id,
            'status' => SubmissionStatusEnum::PROCESSING->value,
            'response_body' => [],
            'function_name' => $functionName,
            'payload' => $payload,
        ]);
        $startTime = Carbon::now();
        try {
            $result = $this->client->invoke([
                'FunctionName' => $functionName,
                'InvocationType' => $async ? 'Event' : 'RequestResponse',
                'Payload' => json_encode($payload),
            ]);
            $payload = $result->get('Payload');
            if ($payload instanceof StreamInterface) {
                $response = json_decode($payload->getContents(), true);
            } else {
                $response = json_decode($payload, true);
            }
        } catch (Exception $e) {
            $response = [
                'statusCode' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'body' => [
                    'Response' => $e->getMessage()
                ],
                'isBase64Encoded' => false,
            ];
        }
        if (empty($response)) {
            $response = [
                'statusCode' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'body' => [
                    'Response' => 'Internal Server Error'
                ],
                'isBase64Encoded' => false,
            ];
        }

        // Capture AWS request ID for both async and sync invocations
        $awsRequestId = $result['@metadata']['headers']['x-amzn-requestid'] ?? null;
        $submissionLog->update([
            'aws_request_id' => $awsRequestId,
        ]);
        if ($async) {
            //if we have an internal server error, we need to put the submission back in queue
            if ($response['statusCode'] === Response::HTTP_INTERNAL_SERVER_ERROR) {
                $this->submission->update([
                    'status' => SubmissionStatusEnum::PENDING->value,
                    'status_code' => Response::HTTP_INTERNAL_SERVER_ERROR,
                ]);
            }
        }
        if (!$async) {
            $submissionLog->update([
                'status_code' => $response['statusCode'] ?? Response::HTTP_INTERNAL_SERVER_ERROR,
                'response_body' => $response,
                'duration_ms' => abs(Carbon::now()->diffInMilliseconds($startTime)),
            ]);

            $handler = EndpointEnum::getEndpointSubmissionLogHandler($this->submission->endpoint_id);
            $handler->handle($submissionLog);
        }
    }

    public function triggerBotViaHTTP(): void
    {
        $apiService = app(ApiService::class);
        $apiKey = config('services.submissions.api_key');
        $payload = $this->buildPayload();

        try {
            //handle BO Filing differently
            //ToDo: remove request parameter when we implement cipc otp
            if ($this->submission->endpoint_id === EndpointEnum::BENEFICIAL_OWNERSHIP_FILING->value && !empty($payload['Otps'])) {
                $path = 'cipc/beneficial-ownership/verify';
            } else {
                $path = EndpointEnum::getSubmissionsEndpointFromId($this->submission->endpoint_id);
            }
            $uri = config('services.submissions.api_base_url') . '/' . $path;

            $response = $apiService->post($uri, $payload, ['x-api-key' => $apiKey]);
            $body = $response->json();
            $keys = array_keys($body);
            if ($keys === range(0, count($body) - 1)) { //means it's a list - validation bag of errors
                throw new Exception('Validation Error occurred: ' . json_encode($body));
            }
            $this->handleInternalServerErrors($body);
        } catch (Exception $e) {
            //recoverable error
            $this->submission->update([
                'status' => SubmissionStatusEnum::PENDING->value,
            ]);
            Log::error($e->getMessage());
        }
    }
}
