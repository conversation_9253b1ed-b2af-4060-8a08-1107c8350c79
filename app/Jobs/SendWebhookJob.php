<?php

namespace App\Jobs;

use App\Models\Webhook;
use App\Services\ApiService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

use App\Utilities\CurlLogger;
use Illuminate\Http\Client\Factory as HttpClient;

class SendWebhookJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    public $tries = 3;
    public Webhook $webhook;

    public function __construct(Webhook $webhook)
    {
        $this->webhook = $webhook;
        $this->onQueue('webhooks');
    }


    /**
     * @throws \Exception
     */
    public function handle(): void
    {
        $this->webhook->update([
            'retry_count' => $this->webhook->retry_count + 1,
            'last_attempt_at' => now(),
        ]);
        $curlLogger = app(CurlLogger::class);
        $httpClient = app(HttpClient::class);
        $webhook = $this->webhook;
        $account = $webhook->submission->account;
        $webhookUrl = $account->webhook_url;

        if (!$account || !$webhookUrl) {
            throw new \Exception('Account webhook URL not found');
        }

        $headers = $account->webhook_url_headers;

        $webhookHttpClient = new ApiService($httpClient, $curlLogger);
        $headers = array_map(function ($header) {
            return $header['key'] . ': ' . $header['value'];
        }, $headers);
        $response = $webhookHttpClient->post(url: $webhookUrl, data: $webhook->payload, headers: $headers);
        $this->webhook->update([
            'url' => $webhookUrl,
            'status_code' => $response->status(),
        ]);
    }

    public function failed(\Exception $exception)
    {
        // Log or handle the failure
    }
}
