<?php

namespace App\Jobs;

use App\Enums\StatusEnum;
use App\Models\ResponseLog;
use App\Services\ApiService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

use App\Utilities\CurlLogger;
use Illuminate\Http\Client\Factory as HttpClient;
use Symfony\Component\HttpFoundation\Response;

class SendResponseLogToInfodocsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    public $tries = 3;
    public ResponseLog $responseLog;

    public function __construct(ResponseLog $responseLog)
    {
        $this->responseLog = $responseLog;
    }


    /**
     * @throws \Exception
     */
    public function handle(): void
    {
        $curlLogger = app(CurlLogger::class);
        $httpClient = app(HttpClient::class);
        $responseLog = $this->responseLog;
        $account = $responseLog->requestLog->account;
        $webhookUrl = $account->webhook_url;

        if (!$account || !$webhookUrl) {
            throw new \Exception('Account webhook URL not found');
        }

        $headers = $account->webhook_url_headers;

        $webhookHttpClient = new ApiService($httpClient, $curlLogger);
        $body = json_decode($responseLog->response_body, true);
        if (!empty($body['body'])) {
            $body = $body['body'];
        } else {
            $body = [
                'Response' => $body
            ];
        }
        $payload = [
            'statusCode' => $responseLog->status_code,
            'reference' => $responseLog->requestLog->reference,
            'body' => $body,
        ];
        $headers = array_map(function ($header) {
            return $header['key'] . ': ' . $header['value'];
        }, $headers);
        $response = $webhookHttpClient->post(url: $webhookUrl, data: $payload, headers: $headers);

        if (!$response->successful()) {
            throw new \Exception('Webhook request failed');
        }
    }

    public function failed(\Exception $exception)
    {
        // Log or handle the failure
    }
}
