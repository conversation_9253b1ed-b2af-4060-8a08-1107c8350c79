<?php

namespace App\Services;

use App\Interfaces\ApiClientInterface;
use App\Utilities\CurlLogger;
use Illuminate\Http\Client\Factory as HttpClient;
use Illuminate\Http\Client\Response;

class ApiService
{

    protected $client;
    protected CurlLogger $curlLogger;
    protected string $apiBaseUrl;


    public function __construct(
        HttpClient $client, //used parameters here to allow unit testing
        CurlLogger $curlLogger
    ) {
        $this->client = $client;
        $this->curlLogger = $curlLogger;
    }

    public function setUrl(string $apiBaseUrl): void
    {
        $this->apiBaseUrl = $apiBaseUrl;
    }

    public function setHeaders(array $headers): void
    {
        $this->client = $this->client->withHeaders($headers);
    }

    public function setParams(string $apiBaseUrl, array $headers): ApiService
    {
        $this->setUrl($apiBaseUrl);
        $this->setHeaders($headers);
        return $this;
    }

    public function get(string $url = '', array $params = [], $headers = null): Response
    {
        if ($headers) {
            $this->setHeaders($headers);
        }

        if (config('services.submissions.enable_submissions')) {
            $params['Testing'] = true;
        }

        // Log the cURL command
        $this->curlLogger->log('GET', $url, $params);

        $client = $this->client;
        // Make the GET request
        return $client->get($url, $params);
    }

    public function post(string $url = '', array $data = [], $headers = null): Response
    {
        if ($headers) {
            $this->setHeaders($headers);
        }
        if (config('services.submissions.environment') === 'testing') {
            $data['Testing'] = true;
        }

        // Log the cURL command
        $this->curlLogger->log('POST', $url, $data);

        // Make the POST request
        return $this->client->post($url, $data);
    }
}
