<?php

namespace App\Services\SubmissionLogHandlers\Cipc;

use App\Enums\RegbotResponseEnum;
use App\Enums\SubmissionStatusEnum;
use App\Models\SubmissionLog;
use App\Services\SubmissionLogHandlers\BaseSubmissionLogHandler;

class AddressChangeSubmissionLogHandler extends BaseSubmissionLogHandler
{

    public function handle(SubmissionLog $submissionLog): void
    {
        $responseBody = $submissionLog->response_body;
        $responseText = $responseBody['body']['Response'] ?? '';
        $payload = [
            'response texts' => '',
            'statusCode' => $submissionLog->status_code->value,
            'reference' => $submissionLog->submission->reference,
            'body' => [
                'CustomerCode' => $responseBody['body']['Response']['CustomerCode'] ?? '',
            ]
        ];
        
        $payload['response texts'] = $responseText;
        $payload['body']['Response'] = $responseText;
        
        if ($responseText === RegbotResponseEnum::ADDRESS_CHANGE_SUBMITTED->value) {
            $payload['response texts'] = RegbotResponseEnum::ADDRESS_CHANGE_SUBMITTED->value;
            $submissionLog->update([
                'status' => SubmissionStatusEnum::SUCCESS->value,
            ]);
            $submissionLog->submission->update([
                'status' => SubmissionStatusEnum::SUCCESS->value,
            ]);

            $this->createWebhook($submissionLog, $payload);
        } elseif ($responseText === RegbotResponseEnum::ADDRESS_CHANGE_DEREGISTERED->value) {
            $payload['response texts'] = RegbotResponseEnum::ADDRESS_CHANGE_DEREGISTERED->value;

            $submissionLog->update([
                'status' => SubmissionStatusEnum::FAILED->value,
            ]);
            $submissionLog->submission->update([
                'status' => SubmissionStatusEnum::FAILED->value,
            ]);
            $this->createWebhook($submissionLog, $payload);
        } else {
            parent::handle($submissionLog);
        }
    }
}
