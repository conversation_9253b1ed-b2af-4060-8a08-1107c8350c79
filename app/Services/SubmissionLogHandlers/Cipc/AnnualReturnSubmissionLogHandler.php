<?php

namespace App\Services\SubmissionLogHandlers\Cipc;

use App\Enums\ResponseStatusEnum;
use App\Enums\SubmissionStatusEnum;
use App\Models\SubmissionLog;
use App\Services\SubmissionLogHandlers\BaseSubmissionLogHandler;

class AnnualReturnSubmissionLogHandler extends BaseSubmissionLogHandler
{

    public function handle(SubmissionLog $submissionLog): void
    {
        $responseBody = $submissionLog->response_body;
        $responseText = $responseBody['body']['Response'] ?? '';
        $payload = [
            'response texts' => '',
            'statusCode' => $submissionLog->status_code->value,
            'reference' => $submissionLog->submission->reference,
            'body' => [
                'CustomerCode' => $responseBody['body']['Response']['CustomerCode'] ?? '',
            ]
        ];
        if (is_string($responseText)) {
            $payload['response texts'] = $responseText;
            $payload['body']['Response'] = $responseText;
        }
        if ($submissionLog->status_code === ResponseStatusEnum::OK || $responseText === 'No annual returns outstanding') {
            $submissionLog->update([
                'status' => SubmissionStatusEnum::SUCCESS->value,
            ]);
            $submissionLog->submission->update([
                'status' => SubmissionStatusEnum::SUCCESS->value,
            ]);
            $this->createWebhook($submissionLog, $payload);
        } else {
            parent::handle($submissionLog);
        }
    }
}
