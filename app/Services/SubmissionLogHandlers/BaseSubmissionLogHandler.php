<?php

namespace App\Services\SubmissionLogHandlers;

use App\Contracts\SubmissionLogHandlerInterface;
use App\Enums\ResponseStatusEnum;
use App\Enums\SubmissionStatusEnum;
use App\Models\SubmissionLog;
use App\Models\Webhook;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class BaseSubmissionLogHandler implements SubmissionLogHandlerInterface
{
    protected array $recoverableErrors = [
        'Internal server error',
        'Service unavailable',
        'Unable to locate element',
        'CIPC site load timed out. Please try again.',
        'HTTPSConnectionPool',
        'invalid session id',
        'Error starting chrome driver',
        'Your session has been expired',
        'Captcha failed',
        'Home Affairs is down',
        'Runtime exited without providing a reason',
        'Runtime.ExitError',
        'CIPC is down',
        'AttributeError',
        'WebDriverException',
        'VALIDATION FAILURE: Please capture required fields.'
    ];

    public function handle(SubmissionLog $submissionLog): void
    {
        $responseBody = $submissionLog->response_body;
        $responseText = $responseBody['body']['Response'] ?? '';
        $payload = [
            'response texts' => '',
            'statusCode' => $submissionLog->status_code->value,
            'reference' => $submissionLog->submission->reference,
            'body' => [
                'CustomerCode' => $responseBody['body']['Response']['CustomerCode'] ?? '',
            ]
        ];
        $payload['response texts'] = $responseText;
        $payload['body']['Response'] = $responseText;
        if ($submissionLog->status_code === ResponseStatusEnum::OK || $submissionLog->status_code === ResponseStatusEnum::DONE) {
            $submissionLog->update([
                'status' => SubmissionStatusEnum::SUCCESS->value,
            ]);
            $submissionLog->submission->update([
                'status' => SubmissionStatusEnum::SUCCESS->value,
            ]);
            $this->createWebhook($submissionLog, $payload);
        } else {
            $submissionLog->update([
                'status' => SubmissionStatusEnum::FAILED->value,
            ]);
            //check if the error is a recoverable error and put back submission in the queue
            if ($this->isRecoverable($responseBody) || $submissionLog->status_code === ResponseStatusEnum::UNKNOWN) {
                $submissionLog->submission->update([
                    'status' => SubmissionStatusEnum::PENDING->value,
                    'submitted_at' => Carbon::now()->addMinutes(intval(config('services.submissions.retry_wait_time')))
                ]);
            } else {
                $submissionLog->submission->update([
                    'status' => SubmissionStatusEnum::FAILED->value,
                ]);
                $this->createWebhook($submissionLog, $payload);
            }
        }
    }

    public function createWebhook(SubmissionLog $submissionLog, array $payload): Webhook|null
    {
        if(empty($payload['response texts'])){
            return null;
        }
        return Webhook::create([
            'submission_id' => $submissionLog->submission->id,
            'payload' => $payload
        ]);
    }

    public function isRecoverable($responseBody): bool
    {
        if (is_array($responseBody)) {
            $responseBody = json_encode($responseBody);
        }
        return Str::contains($responseBody, $this->recoverableErrors);
    }
}
