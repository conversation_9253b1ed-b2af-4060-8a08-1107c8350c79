<?php

namespace App\Services;

class ForeignerAssuranceService
{
    protected string $baseUrl;
    protected array $headers;
    protected ApiService $apiService;

    public function __construct(ApiService $apiService)
    {
        $this->apiService = $apiService;
        // Authentication variables
        $this->baseUrl = config('services.submissions.api_base_url');
        $this->headers = [
            'x-api-key' => config('services.submissions.api_key')
        ];
    }

    public function verify(array $data): array
    {
        $headers = $this->headers;
        $url = $this->baseUrl . '/foreigner-assurance/verify';
        $response = $this->apiService->get($url, $data, $headers);
        $result = $response->json();
        return $result;
    }
}
