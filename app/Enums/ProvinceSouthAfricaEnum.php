<?php

namespace App\Enums;

/**
 * @enum
 * @method static self EASTERN_CAPE() Eastern Cape
 * @method static self FREE_STATE() Free State
 * @method static self GAUTENG() Gauteng
 * @method static self KWAZULU_NATAL() KwaZulu-Natal
 * @method static self LIMPOPO() Limpopo
 * @method static self MPUMALANGA() Mpumalanga
 * @method static self NORTH_WEST() North West
 * @method static self NORTHERN_CAPE() Northern Cape
 * @method static self WESTERN_CAPE() Western Cape
 */
enum ProvinceSouthAfricaEnum: string
{
    case EASTERN_CAPE = 'Eastern Cape';
    case FREE_STATE = 'Free State';
    case GAUTENG = 'Gauteng';
    case KWAZULU_NATAL = 'KwaZulu-Natal';
    case LIMPOPO = 'Limpopo';
    case MPUMALANGA = 'Mpumalanga';
    case NORTH_WEST = 'North West';
    case NORTHERN_CAPE = 'Northern Cape';
    case WESTERN_CAPE = 'Western Cape';


    public static function toArray(): array
    {
        return array_column(self::cases(), 'value');
    }
}
