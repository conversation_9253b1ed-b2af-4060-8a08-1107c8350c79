<?php

namespace App\Enums;

/**
 * @enum
 * @method static self AGRICULTURE() 1 - AG<PERSON>CULTURE, HUNTING FORESTRY AND FISHING
 * @method static self MINING() 2 - MINING AND QUARRYING
 * @method static self MANUFACTURING() 3 - <PERSON><PERSON>FACTURING
 * @method static self ELECTRICITY() 4 - <PERSON>LECT<PERSON>CITY, GAS AND WATER SUPPLY
 * @method static self CONSTRUCTION() 5 - CONSTRUCTION
 * @method static self WHOLESALE() 6 - <PERSON>LESALE AND RETAIL TRADE REPAIR OF MOTOR VEHICLES, MOTOR CYCLES AND PERSONAL AND <PERSON>O<PERSON><PERSON>HOLD GOODS HOTELS AND RESTAURANTS
 * @method static self TRANSPORT() 7 - T<PERSON>NSPORT, STORAGE AND COMMUNICATION
 * @method static self FINANCIAL() 8 - FINANCIAL INTERMEDIATION INSURANCE, REAL ESTATE AND BUSINESS SERVICES
 * @method static self COMMUNITY() 9 - COMMUNITY, SOCIA<PERSON> AND <PERSON><PERSON><PERSON>NAL SERVICES
 * @method static self PRIVATE() 0 - <PERSON><PERSON><PERSON><PERSON> HOUSEHOLDS, <PERSON>XT<PERSON><PERSON><PERSON><PERSON><PERSON> ORGANISATIONS, REPRESENTATIVES OF FOREIGN GOVERNMENTS AND OTHER ACTIVITIES NOT ADEQUATELY DEFINED
 */
enum AnnualReturnAfsIndustryEnum: string
{
    case AGRICULTURE = '1 - AGRICULTURE, HUNTING FORESTRY AND FISHING';
    case MINING = '2 - MINING AND QUARRYING';
    case MANUFACTURING = '3 - MANUFACTURING';
    case ELECTRICITY = '4 - ELECTRICITY, GAS AND WATER SUPPLY';
    case CONSTRUCTION = '5 - CONSTRUCTION';
    case WHOLESALE = '6 - WHOLESALE AND RETAIL TRADE REPAIR OF MOTOR VEHICLES, MOTOR CYCLES AND PERSONAL AND HOUSEHOLD GOODS HOTELS AND RESTAURANTS';
    case TRANSPORT = '7 - TRANSPORT, STORAGE AND COMMUNICATION';
    case FINANCIAL = '8 - FINANCIAL INTERMEDIATION INSURANCE, REAL ESTATE AND BUSINESS SERVICES';
    case COMMUNITY = '9 - COMMUNITY, SOCIAL AND PERSONAL SERVICES';
    case PRIVATE = '0 - PRIVATE HOUSEHOLDS, EXTERRITORIAL ORGANISATIONS, REPRESENTATIVES OF FOREIGN GOVERNMENTS AND OTHER ACTIVITIES NOT ADEQUATELY DEFINED';
}
