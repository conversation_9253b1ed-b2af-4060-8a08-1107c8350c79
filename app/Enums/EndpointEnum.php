<?php

namespace App\Enums;

use App\Contracts\SubmissionLogHandlerInterface;
use App\Services\SubmissionLogHandlers\BaseSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\AccountingOfficerChangeSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\AddressChangeSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\AnnualReturnScrapeSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\AnnualReturnSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\AuditorChangeSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\AuthorisedShareChangeSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\BeneficialOwnershipFilingSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\ChecklistSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\CheckStatusSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\CompanyRegistrationSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\CompanySecretaryChangeSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\DirectorChangeSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\DirectorScrapeSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\DocumentRequestSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\FinancialYearEndSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\MemberChangeSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\MemberScrapeSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\NameChangeSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\NameReservationScrapeSubmissionLogHandler;
use App\Services\SubmissionLogHandlers\Cipc\NameReservationSubmissionLogHandler;
use Exception;

enum EndpointEnum: int
{
    case COMPANY_REGISTRATION = 1;
    case ANNUAL_RETURN = 2;
    case FINANCIAL_YEAR_END = 3;
    case ADDRESS_CHANGE = 4;
    case NAME_CHANGE = 5;
    case DIRECTOR_CHANGE = 6;
    case MEMBER_CHANGE = 7;
    case NAME_RESERVATION = 8;
    case DOCUMENT_REQUEST = 10;
    case CHECK_STATUS = 9; // @deprecated - no longer needed since we started reading COR9.1 documents from CIPC on 6 September 2024
    case AUDITOR_CHANGE = 11;
    case ACCOUNTING_OFFICER_CHANGE = 12;
    case COMPANY_SECRETARY_CHANGE = 13;
    case AUTHORISED_SHARE_CHANGE = 14;
    case CHECKLIST = 15;
    case BENEFICIAL_OWNERSHIP_FILING = 16;
    case DIRECTOR_SCRAPE = 17;
    case MEMBER_SCRAPE = 18;
    case NAME_RESERVATION_SCRAPE = 19;
    case ANNUAL_RETURN_SCRAPE = 20;
    case CREDENTAILS_CHECK = 21;

    public static function values(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    /**
     * Get type enum from id or enum
     * NB: Rather use built-in from() method if you're 100% sure the value is an integer|string
     *
     * @param int|EndpointEnum $value
     *
     * @return EndpointEnum
     */
    public static function fromValue(int|EndpointEnum $value): EndpointEnum
    {
        // handle exceptions
        if ($value instanceof EndpointEnum) {
            return $value;
        }

        return match ($value) {
            self::COMPANY_REGISTRATION->value => self::COMPANY_REGISTRATION,
            self::ANNUAL_RETURN->value => self::ANNUAL_RETURN,
            self::FINANCIAL_YEAR_END->value => self::FINANCIAL_YEAR_END,
            self::ADDRESS_CHANGE->value => self::ADDRESS_CHANGE,
            self::NAME_CHANGE->value => self::NAME_CHANGE,
            self::NAME_RESERVATION->value => self::NAME_RESERVATION,
            self::CHECK_STATUS->value => self::CHECK_STATUS,
            self::DIRECTOR_CHANGE->value => self::DIRECTOR_CHANGE,
            self::MEMBER_CHANGE->value => self::MEMBER_CHANGE,
            self::AUDITOR_CHANGE->value => self::AUDITOR_CHANGE,
            self::ACCOUNTING_OFFICER_CHANGE->value => self::ACCOUNTING_OFFICER_CHANGE,
            self::COMPANY_SECRETARY_CHANGE->value => self::COMPANY_SECRETARY_CHANGE,
            self::AUTHORISED_SHARE_CHANGE->value => self::AUTHORISED_SHARE_CHANGE,
            self::CHECKLIST->value => self::CHECKLIST,
            self::BENEFICIAL_OWNERSHIP_FILING->value => self::BENEFICIAL_OWNERSHIP_FILING,
            self::DOCUMENT_REQUEST->value => self::DOCUMENT_REQUEST,
            self::DIRECTOR_SCRAPE->value => self::DIRECTOR_SCRAPE,
            self::MEMBER_SCRAPE->value => self::MEMBER_SCRAPE,
            self::NAME_RESERVATION_SCRAPE->value => self::NAME_RESERVATION_SCRAPE,
            self::ANNUAL_RETURN_SCRAPE->value => self::ANNUAL_RETURN_SCRAPE,
            default => throw new \InvalidArgumentException('Invalid value for TypeEnum: ' . $value),
        };
    }

    /**
     * Get the maximum invocations for a given type
     *
     * @param EndpointEnum $typeEnum
     *
     * @return int
     */
    public static function getMaxInvocations(EndpointEnum $typeEnum): int
    {
        return 10; //All should have a max of 10
        //		return match ($typeEnum) {
        //			self::BO_FILING => 10,
        //			self::DIRECTOR_CHANGE => 10,
        //			self::COREG => 10,
        //			self::AR_FILING => 10,
        //			self::MEMBER_CHANGE => 10,
        //			self::ADDRESS_CHANGE => 10,
        //			self::CHECKLIST => 10,
        //			self::FYE_CHANGE => 10,
        //			self::DOC_REQUEST => 10,
        //			self::NAME_CHANGE => 10,
        //			self::NAME_RESERVATION => 10,
        //			self::CHECK_STATUS => 10,
        //			self::NAME_RESERVATION_SCRAPE => 10,
        //		};
    }

    /**
     * Get type id from name
     *
     * @param string $typeName
     *
     * @return EndpointEnum
     */
    public static function getTypeFromName(string $typeName): EndpointEnum
    {
        return match ($typeName) {
            'Registration' => EndpointEnum::COMPANY_REGISTRATION,
            'AnnualReturn' => EndpointEnum::ANNUAL_RETURN,
            'FinYearChange' => EndpointEnum::FINANCIAL_YEAR_END,
            'AddressChange' => EndpointEnum::ADDRESS_CHANGE,
            'NameChange' => EndpointEnum::NAME_CHANGE,
            'NameReserve' => EndpointEnum::NAME_RESERVATION,
            'CheckStatus' => EndpointEnum::CHECK_STATUS,
            'DirectorChange' => EndpointEnum::DIRECTOR_CHANGE,
            'MemberChange' => EndpointEnum::MEMBER_CHANGE,
            'AuditorChange' => EndpointEnum::AUDITOR_CHANGE,
            'AccOfficerChange' => EndpointEnum::ACCOUNTING_OFFICER_CHANGE,
            'CoSecChange' => EndpointEnum::COMPANY_SECRETARY_CHANGE,
            'AuthShareChange' => EndpointEnum::AUTHORISED_SHARE_CHANGE,
            'Checklist' => EndpointEnum::CHECKLIST,
            'BeneficialOwnership' => EndpointEnum::BENEFICIAL_OWNERSHIP_FILING,
            'DocumentRequest' => EndpointEnum::DOCUMENT_REQUEST,
            'DirectorScrape' => EndpointEnum::DIRECTOR_SCRAPE,
            'MemberScrape' => EndpointEnum::MEMBER_SCRAPE,
            'NameReservationScrape' => EndpointEnum::NAME_RESERVATION_SCRAPE,
        };
    }

    /**
     * Get type name from id
     *
     * @param EndpointEnum|int $type
     *
     * @return string
     */
    public static function getNameFromId(EndpointEnum|int $typeId): string
    {
        if ($typeId instanceof EndpointEnum) {
            $typeId = $typeId->value;
        }

        return match ($typeId) {
            EndpointEnum::COMPANY_REGISTRATION->value => 'CompanyRegistration',
            EndpointEnum::ANNUAL_RETURN->value => 'AnnualReturnAfs',
            EndpointEnum::FINANCIAL_YEAR_END->value => 'FinancialYearEnd',
            EndpointEnum::ADDRESS_CHANGE->value => 'AddressChange',
            EndpointEnum::NAME_CHANGE->value => 'NameChange',
            EndpointEnum::NAME_RESERVATION->value => 'NameReservation',
            EndpointEnum::CHECK_STATUS->value => 'CheckStatus',
            EndpointEnum::DIRECTOR_CHANGE->value => 'DirectorChange',
            EndpointEnum::MEMBER_CHANGE->value => 'MemberChange',
            EndpointEnum::AUDITOR_CHANGE->value => 'AuditorChange',
            EndpointEnum::ACCOUNTING_OFFICER_CHANGE->value => 'AccOfficerChange',
            EndpointEnum::COMPANY_SECRETARY_CHANGE->value => 'CoSecChange',
            EndpointEnum::AUTHORISED_SHARE_CHANGE->value => 'AuthShareChange',
            EndpointEnum::CHECKLIST->value => 'Checklist',
            EndpointEnum::BENEFICIAL_OWNERSHIP_FILING->value => 'BeneficialOwnership',
            EndpointEnum::DOCUMENT_REQUEST->value => 'DocumentRequest',
            EndpointEnum::DIRECTOR_SCRAPE->value => 'DirectorScrape',
            EndpointEnum::MEMBER_SCRAPE->value => 'MemberScrape',
            EndpointEnum::NAME_RESERVATION_SCRAPE->value => 'NameReservationScrape',
            EndpointEnum::ANNUAL_RETURN_SCRAPE->value => 'ANNUALRETURNScrape',
            EndpointEnum::CREDENTAILS_CHECK->value => 'LoginCredentialsChecker',
        };
    }

    /**
     * Get type name from id
     *
     * @param EndpointEnum|int $type
     *
     * @return string
     * @throws Exception
     */
    public static function getSubmissionsEndpointFromId(EndpointEnum|int $typeId): string
    {
        if ($typeId instanceof EndpointEnum) {
            $typeId = $typeId->value;
        }

        return match ($typeId) {
            EndpointEnum::ADDRESS_CHANGE->value => 'cipc/address-change',
            EndpointEnum::ANNUAL_RETURN->value => 'cipc/annual-return',
            EndpointEnum::ACCOUNTING_OFFICER_CHANGE->value,
            EndpointEnum::AUDITOR_CHANGE->value,
            EndpointEnum::COMPANY_SECRETARY_CHANGE->value,
            EndpointEnum::AUTHORISED_SHARE_CHANGE->value,
            EndpointEnum::DIRECTOR_SCRAPE->value,
            EndpointEnum::MEMBER_SCRAPE->value
            => throw new Exception('Not implemented'),
            EndpointEnum::BENEFICIAL_OWNERSHIP_FILING->value => 'cipc/beneficial-ownership',
            EndpointEnum::CHECKLIST->value => 'cipc/audit-check-list',
            EndpointEnum::CHECK_STATUS->value => 'cipc/check-status',
            EndpointEnum::COMPANY_REGISTRATION->value => 'cipc/new-company-registration',
            EndpointEnum::DIRECTOR_CHANGE->value => 'director-change',
            EndpointEnum::DOCUMENT_REQUEST->value => 'cipc/document-request',
            EndpointEnum::FINANCIAL_YEAR_END->value => 'cipc/financial-year-end',
            EndpointEnum::MEMBER_CHANGE->value => 'cipc/member-change',
            EndpointEnum::NAME_CHANGE->value => 'cipc/name-change',
            EndpointEnum::NAME_RESERVATION->value => 'cipc/name-reservation',
            EndpointEnum::NAME_RESERVATION_SCRAPE->value => 'cipc/name-reservation-scrape',
            EndpointEnum::ANNUAL_RETURN_SCRAPE->value => 'cipc/annual-return-scrape',
        };
    }

    public static function getEndpointIdFromSubmissionsEndpoint(string $endpoint): int
    {
        return match ($endpoint) {
            'cipc/address-change' => EndpointEnum::ADDRESS_CHANGE->value,
            'cipc/annual-return' => EndpointEnum::ANNUAL_RETURN->value,
            'cipc/annual-return/fas' => EndpointEnum::ANNUAL_RETURN->value,
            'cipc/annual-return/afs' => EndpointEnum::ANNUAL_RETURN->value,
            'cipc/beneficial-ownership' => EndpointEnum::BENEFICIAL_OWNERSHIP_FILING->value,
            'cipc/beneficial-ownership/verify' => EndpointEnum::BENEFICIAL_OWNERSHIP_FILING->value,
            'cipc/audit-check-list' => EndpointEnum::CHECKLIST->value,
            'cipc/check-status' => EndpointEnum::CHECK_STATUS->value,
            'cipc/company-registration' => EndpointEnum::COMPANY_REGISTRATION->value,
            'cipc/director-change' => EndpointEnum::DIRECTOR_CHANGE->value,
            'cipc/director-change/verify' => EndpointEnum::DIRECTOR_CHANGE->value,
            'director-change' => EndpointEnum::DIRECTOR_CHANGE->value,
            'director-change-verify' => EndpointEnum::DIRECTOR_CHANGE->value,
            'cipc/document-request' => EndpointEnum::DOCUMENT_REQUEST->value,
            'cipc/financial-year-end' => EndpointEnum::FINANCIAL_YEAR_END->value,
            'cipc/member-change' => EndpointEnum::MEMBER_CHANGE->value,
            'cipc/name-change' => EndpointEnum::NAME_CHANGE->value,
            'cipc/name-reservation' => EndpointEnum::NAME_RESERVATION->value,
            'cipc/name-reservation-scrape' => EndpointEnum::NAME_RESERVATION_SCRAPE->value,
            'cipc/annual-return/scrape' => EndpointEnum::ANNUAL_RETURN_SCRAPE->value,
            'cipc/compliance-checklist' => EndpointEnum::CHECKLIST->value,
            'cipc/verify-login-credentials' => EndpointEnum::CREDENTAILS_CHECK->value,
            default => throw new Exception('Invalid endpoint: ' . $endpoint),
        };
    }

    /**
     * Get submission type ids for KPI
     *
     * @return array
     */
    public static function getSubmissionTypeIds(): array
    {
        return [
            self::COMPANY_REGISTRATION->value,
            self::ANNUAL_RETURN->value,
            self::FINANCIAL_YEAR_END->value,
            self::ADDRESS_CHANGE->value,
            self::NAME_CHANGE->value,
            self::DIRECTOR_CHANGE->value,
            self::MEMBER_CHANGE->value,
            self::NAME_RESERVATION->value,
            self::DOCUMENT_REQUEST->value,
            self::AUDITOR_CHANGE->value,
            self::ACCOUNTING_OFFICER_CHANGE->value,
            self::COMPANY_SECRETARY_CHANGE->value,
            self::AUTHORISED_SHARE_CHANGE->value,
            self::CHECKLIST->value,
            self::BENEFICIAL_OWNERSHIP_FILING->value,
        ];
    }

    public static function getMigratedTypes(): array
    {
        return [
            self::ANNUAL_RETURN->value, // 31
            self::ADDRESS_CHANGE->value, // 33
            self::NAME_CHANGE->value, // 34
            self::DIRECTOR_CHANGE->value, // 35
            self::MEMBER_CHANGE->value, // 36
            self::NAME_RESERVATION->value, // 37
            self::DOCUMENT_REQUEST->value, // 39
            self::BENEFICIAL_OWNERSHIP_FILING->value, // 45
            self::COMPANY_REGISTRATION->value, // 30
            self::FINANCIAL_YEAR_END->value, // 32
            // TODO:
            // self::AUTH_CHANGE->value, // 43
            // self::CHECK_STATUS->value, // 38
            // self::CHECKLIST->value, // 44
        ];
    }

    /**
     * Get paid transaction type enums
     *
     * @return array
     */
    public static function getPaidTransactionTypes(): array
    {
        return [
            self::COMPANY_REGISTRATION,
            self::ANNUAL_RETURN,
            self::FINANCIAL_YEAR_END,
            self::NAME_CHANGE,
            self::NAME_RESERVATION,
            self::DOCUMENT_REQUEST,
            self::AUTHORISED_SHARE_CHANGE,
        ];
    }

    /**
     * Get automated transaction type enums
     *
     * @return array
     */
    public static function getAutomatedTransactions(): array
    {
        return [
            self::BENEFICIAL_OWNERSHIP_FILING,
            self::DIRECTOR_CHANGE,
            // TODO:
            // self::COREG,
            // self::AR_FILING,
            // self::FYE_CHANGE,
            // self::ADDRESS_CHANGE,
            // self::NAME_RESERVATION,
            // self::NAME_CHANGE,
            // self::MEMBER_CHANGE,
            // self::CHECK_STATUS,
            // self::CHECKLIST,
            // self::DOC_REQUEST,
            // self::NAME_RESERVATION_SCRAPE
            // // DEPRECATED
            // // self::DIRECTOR_SCRAPE,
            // // self::MEMBER_SCRAPE,
        ];
    }

    /**
     * Get transaction types that do not require supporting documents
     *
     * @return array ints
     */
    public static function getNoSupportingDocsTypeIds(): array
    {
        return [
            self::ANNUAL_RETURN->value,
            self::FINANCIAL_YEAR_END->value,
            self::ADDRESS_CHANGE->value,
            self::NAME_CHANGE->value,
            self::NAME_RESERVATION->value,
            self::DOCUMENT_REQUEST->value,
            self::CHECKLIST->value,
        ];
    }

    /**
     * Get transaction types that require supporting documents
     *
     * @return array ints
     */
    public static function getSupportingDocsTypeIds(): array
    {
        return [
            self::COMPANY_REGISTRATION->value,
            self::NAME_CHANGE->value,
            self::DIRECTOR_CHANGE->value,
            self::MEMBER_CHANGE->value,
            self::AUDITOR_CHANGE->value,
            self::ACCOUNTING_OFFICER_CHANGE->value,
            self::COMPANY_SECRETARY_CHANGE->value,
            self::AUTHORISED_SHARE_CHANGE->value,
            self::BENEFICIAL_OWNERSHIP_FILING->value,
        ];
    }

    /**
     * Get transaction types that require supporting documents
     *
     * @return array ints
     */
    public static function getSupportingDocsBeforeSubmissionTypeIds(): array
    {
        return [
            self::COMPANY_SECRETARY_CHANGE->value,
            self::DIRECTOR_CHANGE->value,
            self::AUDITOR_CHANGE->value,
            self::ACCOUNTING_OFFICER_CHANGE->value,
            self::BENEFICIAL_OWNERSHIP_FILING->value,
        ];
    }

    /**
     * Get transaction types that require supporting documents before initial submission
     *
     * @return array enums
     */
    public static function getSupportingDocsRequiredBeforeSubmissionTypes(): array
    {
        return [
            self::DIRECTOR_CHANGE,
            self::BENEFICIAL_OWNERSHIP_FILING,
            // TODO: update to accept upload supporting documents
            // self::MEMBER_CHANGE,
            // self::AUDITOR_CHANGE,
            // self::ACC_OFFICER_CHANGE,
            // self::COSEC_CHANGE,
            // self::AUTH_CHANGE,
        ];
    }

    /**
     * Get transaction types that are pointing to the "old" submissions endpoint system
     *
     * @return array integers
     */
    public static function getEServicesClassicTransactions(): array
    {
        return [
            self::ADDRESS_CHANGE->value,
            self::CHECK_STATUS->value,
            self::COMPANY_REGISTRATION->value,
            self::DOCUMENT_REQUEST->value,
            self::FINANCIAL_YEAR_END->value,
            self::MEMBER_CHANGE->value,
            self::NAME_CHANGE->value,
            self::NAME_RESERVATION->value,
            self::NAME_RESERVATION_SCRAPE->value,
            self::CREDENTAILS_CHECK->value,
        ];
    }

    public static function getSDKTransactionTypes(): array
    {
        return [
            self::COMPANY_REGISTRATION->value,
            self::ANNUAL_RETURN->value,
            self::FINANCIAL_YEAR_END->value,
            self::ADDRESS_CHANGE->value,
            self::NAME_CHANGE->value,
            self::DIRECTOR_CHANGE->value,
            self::MEMBER_CHANGE->value,
            // self::NAME_RESERVATION->value,
            self::DOCUMENT_REQUEST->value,
            // self::CHECK_STATUS ->value,
            // self::AUDITOR_CHANGE->value,
            // self::ACCOUNTING_OFFICER_CHANGE->value,
            // self::COMPANY_SECRETARY_CHANGE->value,
            // self::AUTHORISED_SHARE_CHANGE->value,
            self::CHECKLIST->value,
            self::BENEFICIAL_OWNERSHIP_FILING->value,
            // self::DIRECTOR_SCRAPE->value,
            // self::MEMBER_SCRAPE->value,
            // self::NAME_RESERVATION_SCRAPE->value,
            // self::ANNUAL_RETURN_SCRAPE->value
            self::CREDENTAILS_CHECK->value,
        ];
    }

    public static function getEndpointSubmissionLogHandler(EndpointEnum|int $endpointId): SubmissionLogHandlerInterface
    {
        if ($endpointId instanceof EndpointEnum) {
            $endpointId = $endpointId->value;
        }
        return match ($endpointId) {
            self::COMPANY_REGISTRATION->value => app(CompanyRegistrationSubmissionLogHandler::class),
            self::ANNUAL_RETURN->value => app(AnnualReturnSubmissionLogHandler::class),
            self::FINANCIAL_YEAR_END->value => app(FinancialYearEndSubmissionLogHandler::class),
            self::ADDRESS_CHANGE->value => app(AddressChangeSubmissionLogHandler::class),
            self::NAME_CHANGE->value => app(NameChangeSubmissionLogHandler::class),
            self::DIRECTOR_CHANGE->value => app(DirectorChangeSubmissionLogHandler::class),
            self::MEMBER_CHANGE->value => app(MemberChangeSubmissionLogHandler::class),
            self::NAME_RESERVATION->value => app(NameReservationSubmissionLogHandler::class),
            self::DOCUMENT_REQUEST->value => app(DocumentRequestSubmissionLogHandler::class),
            self::CHECK_STATUS->value => app(CheckStatusSubmissionLogHandler::class),
            self::AUDITOR_CHANGE->value => app(AuditorChangeSubmissionLogHandler::class),
            self::ACCOUNTING_OFFICER_CHANGE->value => app(AccountingOfficerChangeSubmissionLogHandler::class),
            self::COMPANY_SECRETARY_CHANGE->value => app(CompanySecretaryChangeSubmissionLogHandler::class),
            self::AUTHORISED_SHARE_CHANGE->value => app(AuthorisedShareChangeSubmissionLogHandler::class),
            self::CHECKLIST->value => app(ChecklistSubmissionLogHandler::class),
            self::BENEFICIAL_OWNERSHIP_FILING->value => app(BeneficialOwnershipFilingSubmissionLogHandler::class),
            self::DIRECTOR_SCRAPE->value => app(DirectorScrapeSubmissionLogHandler::class),
            self::MEMBER_SCRAPE->value => app(MemberScrapeSubmissionLogHandler::class),
            self::NAME_RESERVATION_SCRAPE->value => app(NameReservationScrapeSubmissionLogHandler::class),
            self::ANNUAL_RETURN_SCRAPE->value => app(AnnualReturnScrapeSubmissionLogHandler::class),
            default => app(BaseSubmissionLogHandler::class),
        };
    }
}
