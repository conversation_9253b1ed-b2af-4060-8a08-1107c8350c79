<?php

namespace App\Enums;

/**
 * @enum
 * @method static self SHAREHOLDING()          Shareholding/Beneficial Ownership (requires numeric value between 5-100)
 * @method static self VOTING_RIGHTS()         Voting right (requires string value, max 10 digits)
 * @method static self BOARD_APPOINTMENT()     Appointment of board
 * @method static self OTHER_INFLUENCE()       Other influence or control
 * @method static self SENIOR_OFFICIAL()       Senior managing official
 * @method static self TRUST_SETTLOR()         Settlor of trust
 * @method static self TRUST_TRUSTEE()         Trustee of a trust
 * @method static self TRUST_PROTECTOR()       Protector of a trust
 * @method static self TRUST_BENEFICIARY()     Beneficiary of a trust
 * @method static self TRUST_OTHER_INFLUENCE() Other influence or control of a trust
 * @method static self RIGHTS_SURPLUS_ASSETS() Rights to surplus assets on dissolution
 * @method static self RIGHTS_PROFITS()        Rights to receive profits or income
 * @method static self RIGHTS_CONTRACT()       Rights granted by contract
 * @method static self RIGHTS_CONTRACT_CONDITIONAL() Conditional rights granted by contract
 */
enum InterestTypeEnum: string
{
    case SHAREHOLDING = 'Shareholding/Beneficial Ownership'; // requires integer value, between 5 and 100
    case VOTING_RIGHTS = 'Voting right'; // requires string value, 10 digits max
    case BOARD_APPOINTMENT = 'Appointment of board';
    case OTHER_INFLUENCE = 'Other influence or control';
    case SENIOR_OFFICIAL = 'Senior managing official';
    case TRUST_SETTLOR = 'Settlor of trust';
    case TRUST_TRUSTEE = 'Trustee of a trust';
    case TRUST_PROTECTOR = 'Protector of a trust';
    case TRUST_BENEFICIARY = 'Beneficiary of a trust';
    case TRUST_OTHER_INFLUENCE = 'Other influence or control of a trust';
    case RIGHTS_SURPLUS_ASSETS = 'Rights to surplus assets on dissolution';
    case RIGHTS_PROFITS = 'Rights to receive profits or income';
    case RIGHTS_CONTRACT = 'Rights granted by contract';
    case RIGHTS_CONTRACT_CONDITIONAL = 'Conditional rights granted by contract';

    /**
     * Get all interest types as an array
     *
     * @return array<string>
     */
    public static function toArray(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }
}
