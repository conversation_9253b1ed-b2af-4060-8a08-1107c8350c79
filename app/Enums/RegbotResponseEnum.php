<?php

namespace App\Enums;

use Illuminate\Support\Str;

enum RegbotResponseEnum: string
{
	// General
	case CAPTCHA_FAILED = 'Captcha failed. Please try again.';
	case CIPC_DOWN = 'CIPC is down. Please try again.';
	case CIPC_LOAD_TIMEOUT = 'CIPC site load timed out. Please try again.';
	case HOME_AFFAIRS_DOWN = 'Home Affairs is down. Please try again.';
	case NOT_ENOUGH_MONEY = 'Not enough money in your CIPC account.';
	case INVALID_ISSUE_DATE = 'Invalid issue date';
	case ERROR_CHROME_DRIVER = 'Error starting chrome driver';

	// Address Changes
	case ADDRESS_CHANGE_SUBMITTED = 'Address change submitted successfully';
	case ADDRESS_CHANGE_DEREGISTERED = 'The indicated entity cannot file address changes since it has been finally deregistered.';

	// Director Changes
	case DIRECTOR_CHANGE_PENDING_APPLICATION = 'Pending application submitted by customer';
	case DIRECTOR_DETAILS_DO_NOT_MATCH = 'Contact details do not match';
	case DIRECTOR_NOT_FOUND = 'Director not found';
	case DIRECTOR_COUNT_DOES_NOT_MATCH = 'Number of directors does not match table';

	// Document Requests
	case DOC_REQUEST_MOI_NOT_AVAILABLE = 'Eletronic MOI documents not available for this company';

	// Beneficial Ownership
	case BO_FILING_OTP_VERIFIED = 'OTPs verified';
	case BO_FILING_OTP_REVIEW = 'OTP review';
	case BO_FILING_SAVED_TO_S3 = 'Saved certificates to S3';
	case BO_FILING_SAVED_TO_S3_FAILED = 'Failed to save certificates to S3';
	case BO_FILING_NOT_FOUND = 'No applications found';
	case BO_FILING_MISSING_DOCS = 'Missing Files';
	case BO_FILING_DISCARDED = 'The application with the reference number has been discarded.';
	case BO_FILING_AR_FINAL_DEREGISTRATION = 'AR Final deregistration';
	case BO_FILING_OTP_INCOMPLETE = 'Incomplete OTP verification';
	case BO_FILING_INCORRECT_BOS = 'Incorrect number of BOs';
	case BO_FILING_MANDATE_FILE_ERROR = 'Mandate file error';
	case BO_FILING_DECLARATION_NOT_UP_TO_DATE = 'Beneficial Ownership Declaration (BO) is not up to date';
	case BO_COMPLETED_APPLICATION_NOT_FOUND = 'Completed application for amendment not found';
	case BO_COMPANY_DEREGISTERED_OR_IN_DEREGISTRATION = 'Unable to file Beneficial Ownership';
	// validation failures to support -> others to information pending/requested
	case BO_FILING_VALIDATION_FAILURE_PROVINCE = 'VALIDATION FAILURE: Please select Province';
	case BO_FILING_VALIDATION_FAILURE_REQUIRED_FIELDS = 'VALIDATION FAILURE: Please capture required fields.';
	case BO_FILING_VALIDATION_FAILURE_NATURAL_PERSONS_EXIST = 'VALIDATION FAILURE: 90004: non-ORACLE exception Natural Person already exist for this BO.';
	case BO_FILING_VALIDATION_FAILURE_NATURAL_PERSONS = 'VALIDATION FAILURE: Natural person details could not be saved. Input string was not in a correct format.';
	case BO_FILING_VALIDATION_FAILURE_TOTAL_OWNERSHIP_PERCENTAGE = 'VALIDATION FAILURE: Total ownership percentage cannot be more than 100%. Please capture correct value.';
	case BO_FILING_VALIDATION_SESSION_EXPIRED = "VALIDATION FAILURE: Your session has been expired. Please login again!";

	// Check Status
	case NAME_RESERVATION_APPROVED = 'approved';
	case NAME_RESERVATION_REJECTED = 'rejected';
	case NAME_RESERVATION_PENDING = 'pending';

	// Catch all
	case VALIDATION_FAILURE = 'VALIDATION FAILURE';
	case GENERAL_RESPONSE = 'General Response';
	case UNKNOWN_ERROR = 'Unknown Error';

	static function isRecoverableError(string $message): bool
	{
		return strpos($message, 'Please try again.') || in_array($message, [self::CIPC_LOAD_TIMEOUT->value]);
	}
	static function getCategoryFromRequestBody($requestBody): string
	{
		if (Str::contains($requestBody, 'VALIDATION FAILURE', true)) {
			return self::VALIDATION_FAILURE->value;
		}

		foreach (self::cases() as $case) {
			if (Str::contains($requestBody, $case->value, true)) {
				return $case->value;
			}
		}
		return self::GENERAL_RESPONSE->value;
	}
}
