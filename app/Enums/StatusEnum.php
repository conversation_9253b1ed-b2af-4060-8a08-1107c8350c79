<?php

namespace App\Enums;



use App\Models\Status;

enum StatusEnum: int
{
    case SUCCESS = 1;
    case FAILED = 2;
    case CANCELLED = 3;
    case INITIAL = 4;
    case SCHEDULED = 5;
    case REVIEW = 6;
    case FROZEN = 7;
    case INFO_PENDING = 10;
    case INFO_REQUESTED = 11;
    case INFO_SUBMITTED = 12;
    case INFO_REVIEW = 13;
    case INFO_APPROVED = 14;
    case INFO_REJECTED = 15;
    case INFO_SUPPORT = 16;
    case DOCS_PENDING = 20;
    case DOCS_REQUESTED = 21;
    case DOCS_SUBMITTED = 22;
    case DOCS_REVIEW = 23;
    case DOCS_APPROVED = 24;
    case DOCS_REJECTED = 25;
    case DOCS_SUPPORT = 26;
    case SIGN_PENDING = 30;
    case SIGN_REQUESTED = 31;
    case SIGN_SUBMITTED = 32;
    case SIGN_REVIEW = 33;
    case SIGN_APPROVED = 34;
    case SIGN_REJECTED = 35;
    case SIGN_SUPPORT = 36;
    case CIPC_PENDING = 40;
    case CIPC_SUBMITTED = 41;
    case CIPC_REVIEW = 42;
    case CIPC_ENQUIRY = 43;
    case CIPC_APPROVED = 44;
    case CIPC_REJECTED = 45;
    case CIPC_SUPPORT = 46;
    case CIPC_EXPIRED = 47;
    case CIPC_BLOCKED = 48;
    case OTP_PENDING = 50;
    case OTP_REQUESTED = 51;
    case OTP_SUBMITTED = 52;
    case OTP_REVIEW = 53;
    case OTP_APPROVED = 54;
    case OTP_REJECTED = 55;
    case OTP_SUPPORT = 56;
    case OTP_EXPIRED = 57;

    public static function values(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    public static function fromValue(int|StatusEnum $value): StatusEnum
    {
        if ($value instanceof StatusEnum) {
            return $value;
        }

        return match ($value) {
            self::SUCCESS->value => self::SUCCESS,
            self::FAILED->value => self::FAILED,
            self::CANCELLED->value => self::CANCELLED,
            self::FROZEN->value => self::FROZEN,
            self::SCHEDULED->value => self::SCHEDULED,
            self::REVIEW->value => self::REVIEW,
            self::INFO_PENDING->value => self::INFO_PENDING,
            self::INFO_REQUESTED->value => self::INFO_REQUESTED,
            self::INFO_SUBMITTED->value => self::INFO_SUBMITTED,
            self::INFO_REVIEW->value => self::INFO_REVIEW,
            self::INFO_APPROVED->value => self::INFO_APPROVED,
            self::INFO_REJECTED->value => self::INFO_REJECTED,
            self::INFO_SUPPORT->value => self::INFO_SUPPORT,
            self::DOCS_PENDING->value => self::DOCS_PENDING,
            self::DOCS_REQUESTED->value => self::DOCS_REQUESTED,
            self::DOCS_SUBMITTED->value => self::DOCS_SUBMITTED,
            self::DOCS_REVIEW->value => self::DOCS_REVIEW,
            self::DOCS_APPROVED->value => self::DOCS_APPROVED,
            self::DOCS_REJECTED->value => self::DOCS_REJECTED,
            self::DOCS_SUPPORT->value => self::DOCS_SUPPORT,
            self::SIGN_PENDING->value => self::SIGN_PENDING,
            self::SIGN_REQUESTED->value => self::SIGN_REQUESTED,
            self::SIGN_SUBMITTED->value => self::SIGN_SUBMITTED,
            self::SIGN_REVIEW->value => self::SIGN_REVIEW,
            self::SIGN_APPROVED->value => self::SIGN_APPROVED,
            self::SIGN_REJECTED->value => self::SIGN_REJECTED,
            self::SIGN_SUPPORT->value => self::SIGN_SUPPORT,
            self::CIPC_PENDING->value => self::CIPC_PENDING,
            self::CIPC_SUBMITTED->value => self::CIPC_SUBMITTED,
            self::CIPC_REVIEW->value => self::CIPC_REVIEW,
            self::CIPC_ENQUIRY->value => self::CIPC_ENQUIRY,
            self::CIPC_APPROVED->value => self::CIPC_APPROVED,
            self::CIPC_REJECTED->value => self::CIPC_REJECTED,
            self::CIPC_SUPPORT->value => self::CIPC_SUPPORT,
            self::CIPC_EXPIRED->value => self::CIPC_EXPIRED,
            self::CIPC_BLOCKED->value => self::CIPC_BLOCKED,
            self::OTP_PENDING->value => self::OTP_PENDING,
            self::OTP_REQUESTED->value => self::OTP_REQUESTED,
            self::OTP_SUBMITTED->value => self::OTP_SUBMITTED,
            self::OTP_REVIEW->value => self::OTP_REVIEW,
            self::OTP_APPROVED->value => self::OTP_APPROVED,
            self::OTP_REJECTED->value => self::OTP_REJECTED,
            self::OTP_SUPPORT->value => self::OTP_SUPPORT,
            self::OTP_EXPIRED->value => self::OTP_EXPIRED,
            default => throw new \InvalidArgumentException('Invalid value for StatusEnum: ' . $value),
        };
    }

    public static function getPendingCipcStatusIds(): array
    {
        return [self::CIPC_PENDING->value, self::OTP_PENDING->value, self::OTP_REQUESTED->value];
    }

    public static function getPendingStatusIds(): array
    {
        return array_diff(self::values(), self::getFinalStatusIds());
    }

    public static function getFinalCipcStatusIds(): array
    {
        return [self::CIPC_APPROVED->value, self::CIPC_REJECTED->value, self::CIPC_EXPIRED->value];
    }

    public static function getFinalInfoDocsStatusIds(): array
    {
        return [self::SUCCESS->value, self::FAILED->value, self::CANCELLED->value];
    }

    public static function getFinalStatusIds(): array
    {
        return array_merge(self::getFinalCipcStatusIds(), self::getFinalInfoDocsStatusIds());
    }

    public static function getSuccessfulStatuses(): array
    {
        return ['Success', 'Information Approved', 'Documents Approved', 'OTP Approved', 'CIPC Approved'];
    }

    public static function getPendingStatuses(): array
    {
        return [
            'Information Pending',
            'Information Requested',
            'Information Submitted',
            'Documents Pending',
            'Documents Requested',
            'Documents Submitted',
            'CIPC Pending',
            'CIPC Submitted',
            'OTP Pending',
            'OTP Requested',
            'OTP Submitted',
        ];
    }

    public static function getSupportStatuses(): array
    {
        return [
            'Frozen',
            'Review',
            'Information Support',
            'Information Review',
            'Documents Support',
            'Documents Review',
            'CIPC Support',
            'OTP Review',
            'OTP Support',
        ];
    }

    public static function getFailedStatuses(): array
    {
        return ['Failed', 'Cancelled', 'CIPC Rejected', 'CIPC Blocked', 'CIPC Expired'];
    }

    public static function statusesForDatatables(): array
    {
        return [
            self::CANCELLED,
            self::FROZEN,
            self::INFO_PENDING,
            self::INFO_REQUESTED,
            self::INFO_SUBMITTED,
            self::DOCS_PENDING,
            self::DOCS_REQUESTED,
            self::DOCS_SUBMITTED,
            self::CIPC_PENDING,
            self::CIPC_SUBMITTED,
            self::CIPC_REVIEW,
            self::CIPC_ENQUIRY,
            self::CIPC_APPROVED,
            self::CIPC_REJECTED,
            self::CIPC_SUPPORT,
            self::CIPC_EXPIRED,
            self::CIPC_BLOCKED,
            self::OTP_PENDING,
            self::OTP_REQUESTED,
            self::OTP_SUBMITTED,
            self::OTP_REVIEW,
        ];
    }

    public function description(): string
    {
        return Status::find($this->value)->name;
    }
}
