<?php

namespace App\Enums;

enum ResponseStatusEnum: int
{
    case DEFAULT = 100;
    case OK = 200;
    case DONE = 201;
    case LOGIN_FAILED = 401;
    case INSUFFICIENT_FUNDS = 402;
    case FORBIDDEN = 403;
    case MISSING = 404;
    case CONFLICT = 409;
    case UNKNOWN = 500;
    case ERROR = 502;
    case UNPROCESSABLE_ENTITY = 422;
    case UNAVAILABLE = 503;

    public static function fromValue(int|string $value): self
    {
        // ensure integer
        if (is_string($value)) {
            $value = (int) $value;
        }

        return match ($value) {
            100 => self::DEFAULT,
            200 => self::OK,
            201 => self::DONE,
            401 => self::LOGIN_FAILED,
            402 => self::INSUFFICIENT_FUNDS,
            403 => self::FORBIDDEN,
            404 => self::MISSING,
            409 => self::CONFLICT,
            422 => self::UNPROCESSABLE_ENTITY,
            500 => self::UNKNOWN,
            502 => self::ERROR,
            503 => self::UNAVAILABLE,
            default => throw new \UnexpectedValueException("Unexpected value: {$value}"),
        };
    }
}
