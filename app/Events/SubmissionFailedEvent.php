<?php

namespace App\Events;

use App\Models\RequestLog;
use App\Models\ResponseLog;
use Exception;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SubmissionFailedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ResponseLog $responseLog;
    /**
     * Create a new event instance.
     */
    public function __construct(ResponseLog $responseLog)
    {
        $this->responseLog = $responseLog;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
