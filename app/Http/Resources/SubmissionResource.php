<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubmissionResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'endpoint' => $this->whenLoaded('endpoint'),
            'status' => $this->status->value,
            'reference' => $this->reference,
            'account' => $this->whenLoaded('account'),
            'customer_type' => $this->customer_type->value,
            'customer_id' => $this->customer_id,
            'json_data' => $this->json_data,
            'submitted_at' => $this->submitted_at?->toIso8601String(),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}
