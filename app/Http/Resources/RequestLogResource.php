<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RequestLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'reference' => $this->reference,
            'endpoint' => $this->endpoint->name ?? '',
            'status' => $this->status->name ?? '',
            'status_code' => $this->status_code,
            'request_body' => $this->request_body,
            'created_at' => $this->created_at,
        ];
    }
}
