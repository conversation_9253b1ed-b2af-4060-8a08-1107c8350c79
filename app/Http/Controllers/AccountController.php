<?php

namespace App\Http\Controllers;

use App\Enums\RoleEnum;
use App\Models\Account;
use App\Models\SubscriptionPlan;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Inertia\Inertia;

class AccountController extends Controller
{
    public function index(Request $request)
    {
        $results = Account::with(['subscriptionPlan'])
            ->filter($request->only(['search', 'date_range', 'name']))
            ->orderBy('created_at', 'desc')
            ->paginate();
        return Inertia::render('Accounts/Index', [
            'results' => $results,
            'filters' => $request->only(['search', 'date_range', 'name'])
        ]);
    }

    public function show(Account $account)
    {
        if (Auth::user()->role_id !== 1) {
            abort(404);
        }
        $account->load(['subscriptionPlan']);
        return Inertia::render('Accounts/Show', [
            'account' => $account,
        ]);
    }

    public function create()
    {
        if (Auth::user()->role_id !== 1) {
            abort(404);
        }
        return Inertia::render('Accounts/Create', [
            'plans' => SubscriptionPlan::get(),
        ]);
    }

    public function store(Request $request)
    {
        if (Auth::user()->role_id !== 1) {
            abort(404);
        }
        $request->validate([
            'name' => 'required',
            'contact_name' => 'required',
            'contact_email' => 'required',
            'password' => 'required|confirmed',
            //'subscription_end_date' => 'required',
            //'webhook_url' => 'required',
            'webhook_url_headers' => 'nullable|array',
        ]);
        $account = Account::create([
            'name' => $request->name,
            'webhook_url' => $request->webhook_url,
            'webhook_url_headers' => $request->webhook_url_headers ?? [],
        ]);
        $user = User::create([
            'name' => $request->contact_name,
            'email' => $request->contact_email,
            'password' => bcrypt($request->password ?? Str::password()),
            'account_id' => $account->id,
            'role_id' => RoleEnum::CLIENT->value,
        ]);
        return redirect()->route('accounts.index')->with('success', 'Account created.');
    }

    public function edit(Account $account)
    {
        if (Auth::user()->role_id !== 1) {
            abort(404);
        }
        $account->load(['subscriptionPlan']);
        return Inertia::render('Accounts/Edit', [
            'account' => $account,
            'plans' => SubscriptionPlan::get(),
        ]);
    }

    public function update(Request $request, Account $account)
    {
        if (Auth::user()->role_id !== 1) {
            abort(404);
        }
        $request->validate([
            'name' => 'required',
            //'subscription_end_date' => 'required',
            //'webhook_url' => 'required',
            'webhook_url_headers' => 'nullable|array',
        ]);
        $account->update([
            'name' => $request->name,
            'webhook_url' => $request->webhook_url,
            'webhook_url_headers' => $request->webhook_url_headers ?? [],
        ]);
        return redirect()->route('accounts.index')->with('success', 'Account updated.');
    }

    public function delete(Account $account)
    {
        if (Auth::user()->role_id !== 1) {
            abort(404);
        }
        $account->subscriptions()->delete();
        $account->delete();
        return redirect()->route('accounts.index')->with('success', 'Account deleted.');
    }
}
