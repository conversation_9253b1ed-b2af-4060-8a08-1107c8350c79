<?php

namespace App\Http\Controllers;

use App\Models\RequestLog;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Dashboard', [
            'subscription' => Subscription::with(['subscriptionPlan'])->where('account_id', Auth::user()->account_id)->orderBy('id', 'desc')->first(),
            'latestRequestLogs' => RequestLog::with(['endpoint', 'status'])->where('account_id', Auth::id())->orderBy('id', 'desc')->limit(5)->get(),
        ]);
    }

    public function getDashboardStatistics(Request $request)
    {
        $currentRange = current_range($request->range ?? 30);
        $previousRange = previous_range($request->rang ?? 30);
        $currentRangeTotalRequests = RequestLog::where('account_id', Auth::id())
            ->whereBetween('created_at', [$currentRange[0], $currentRange[1]])
            ->count();
        $previousRangeTotalRequests = RequestLog::where('account_id', Auth::id())
            ->whereBetween('created_at', [$previousRange[0], $previousRange[1]])
            ->count();
        return response()->json([
            'currentRangeTotalRequests' => $currentRangeTotalRequests,
            'previousRangeTotalRequests' => $previousRangeTotalRequests,
        ]);
    }

    public function updateWebhookURL(Request $request)
    {
        $request->validate([
            'webhook_url' => 'required|url',
            'webhook_url_headers' => 'nullable|array',
        ]);
        Auth::user()->account->update([
            'webhook_url' => $request->webhook_url,
            'webhook_url_headers' => $request->webhook_url_headers,
        ]);
        return redirect()->back()->with('success', 'Webhook URL updated successfully');
    }
}
