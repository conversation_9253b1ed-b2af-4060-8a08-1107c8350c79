<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\RequestLogStatusEnum;
use App\Enums\EndpointEnum;
use App\Enums\SubmissionStatusEnum;
use App\Events\SubmissionRequestCreatedEvent;
use App\Http\Requests\AnnualReturnFasRequest;
use App\Models\RequestLog;
use App\Models\Submission;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class AnnualReturnFasController extends CipcController
{
    /**
     * Annual Return FAS Filing
     *
     * This endpoint allows you to submit annual returns along with Financial Accountability Supplement (FAS) to CIPC via eServices.
     *
     * Functionality
     * - Facilitates the submission of a company's annual return, including financial information.
     * - Ensures compliance with CIPC financial reporting regulations.
     *
     * Important Considerations
     * - The FAS must be completed as per CIPC requirements.
     * - Ensure accurate financial data to prevent submission errors or compliance issues.
     *
     * Triggers a [Annual Return FAS Filing](#webhooks-POSTyour-webhook-url#annual-return-fas) webhook if successful.
     *
     * @group CIPC Endpoints
     *
     * @response 201 {
     *   "message": "Annual return filing request has been received.",
     *   "submission_id": 123,
     *   "status": "SCHEDULED"
     * }
     *
     * @response 403 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "ERROR: Invalid enterprise number."
     *   }
     */
    public function index(AnnualReturnFasRequest $request)
    {
        try {
            $requestLog = RequestLog::findOrFail($request->attributes->get('request_log_id'));
            $submission = Submission::where('reference', $requestLog->reference)
                ->where('account_id', $requestLog->account_id)
                ->where('endpoint_id', $requestLog->endpoint_id)
                ->latest()
                ->first();
            if ($submission && $submission->status === SubmissionStatusEnum::PROCESSING) {
                return response()->json([
                    'message' => 'A submission with the same reference is already being processed.',
                    'submission_id' => $submission->id,
                    'status' => SubmissionStatusEnum::PROCESSING->value,
                ], ResponseAlias::HTTP_CONFLICT);
            }
            $submission = Submission::updateOrCreate([
                'reference' => $requestLog->reference,
                'account_id' => $requestLog->account_id,
                'endpoint_id' => $requestLog->endpoint_id,
            ], [
                'status' => SubmissionStatusEnum::PENDING->value,
                'request_log_id' => $requestLog->id,
                'json_data' => $requestLog->request_body,
                'is_test' => $requestLog->is_test,
            ]);

            return response()->json([
                'message' => 'Annual return filing request has been received.',
                'submission_id' => $submission->id,
                'status' => SubmissionStatusEnum::PENDING->value,
            ], ResponseAlias::HTTP_CREATED);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'An error occurred while processing your request.'
            ], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
