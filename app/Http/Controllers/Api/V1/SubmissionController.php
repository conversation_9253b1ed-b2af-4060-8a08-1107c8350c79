<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\SubmissionResource;
use App\Models\Submission;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class SubmissionController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $submissions = Submission::with(['endpoint', 'account'])
            ->latest()
            ->paginate();

        return SubmissionResource::collection($submissions);
    }

    public function show(Submission $submission): SubmissionResource
    {
        return new SubmissionResource($submission->load(['endpoint', 'account']));
    }
}
