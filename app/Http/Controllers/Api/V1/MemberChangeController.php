<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\RequestLogStatusEnum;
use App\Enums\SubmissionStatusEnum;
use App\Http\Requests\MemberChangeRequest;
use App\Models\RequestLog;
use App\Models\Submission;
use Exception;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class MemberChangeController extends CipcController
{
    /**
     * Member Change
     *
     * This endpoint allows you to update a company's members and member details via CIPC eServices.
     *
     * Functionality
     * - Add new members to an existing Close Corporation (CC).
     * - Remove existing members from a CC.
     * - Update member details.
     * - Update shareholding percentages.
     * - Transfer membership interests between members.
     *
     * Important Considerations
     * - Ensure all member details are accurate and up to date before submission.
     * - Membership changes must comply with CIPC regulations for Close Corporations.
     *
     * Triggers a [Member Change](#webhooks-POSTyour-webhook-url%23member-change) webhook if successful.
     *
     * @group CIPC Endpoints
     *
     * @response 201 {
     *   "message": "Member change request has been received.",
     *   "submission_id": 123,
     *   "status": "SCHEDULED"
     * }
     *
     * @response 422 {
     *   "message": "The total member size does not equal 100.",
     *   "errors": {
     *   }
     * }
     */
    public function index(MemberChangeRequest $request)
    {
        try {
            $requestLog = RequestLog::findOrFail($request->attributes->get('request_log_id'));
            $submission = Submission::where('reference', $requestLog->reference)
                ->where('account_id', $requestLog->account_id)
                ->where('endpoint_id', $requestLog->endpoint_id)
                ->latest()
                ->first();
            if ($submission && $submission->status === SubmissionStatusEnum::PROCESSING) {
                return response()->json([
                    'message' => 'A submission with the same reference is already being processed.',
                    'submission_id' => $submission->id,
                    'status' => SubmissionStatusEnum::PROCESSING->value,
                ], ResponseAlias::HTTP_CONFLICT);
            }
            $submission = Submission::updateOrCreate([
                'reference' => $requestLog->reference,
                'account_id' => $requestLog->account_id,
                'endpoint_id' => $requestLog->endpoint_id,
            ], [
                'status' => SubmissionStatusEnum::PENDING->value,
                'request_log_id' => $requestLog->id,
                'json_data' => $requestLog->request_body,
                'is_test' => $requestLog->is_test,
            ]);

            return response()->json([
                'message' => 'Member Change request has been received.',
                'submission_id' => $submission->id,
                'status' => SubmissionStatusEnum::PENDING->value,
            ], ResponseAlias::HTTP_CREATED);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'An error occurred while processing your request.'
            ], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

}
