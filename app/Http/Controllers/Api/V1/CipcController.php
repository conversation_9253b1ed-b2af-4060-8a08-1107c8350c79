<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\StatusEnum;
use App\Enums\EndpointEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\AddressChangeRequest;
use App\Http\Requests\CompanyRegistrationRequest;
use App\Jobs\SendToLambdasJob;
use App\Models\PersonalAccessToken;
use App\Models\RequestLog;
use Illuminate\Http\Request;

class CipcController extends Controller
{
    /**
     * Creates an array with common request data
     * @LRDresponses 200|400|401|403|404|422|500
     */
    public function getRequestData(Request $request)
    {
        $user = $request->user();
        $accountId = $user->account_id;
        $reference = $request->reference;
        if ($request->transactionId) {
            $reference = $request->transactionId;
        }

        return [
            'personal_access_token_id' => $request->user()->currentAccessToken()->id,
            'status_id' => StatusEnum::SCHEDULED,
            'account_id' => $accountId,
            'reference' => $reference,
            'tracking_no' => $request->tracking_no,
            'is_test' => $request->is_test ? 1 : 0,
            'ip_address' => $request->ip(),
            'request_body' => json_encode($request->all()),
            'response_body' => json_encode([]),
        ];
    }
}
