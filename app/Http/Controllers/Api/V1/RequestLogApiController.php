<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\RequestLogResource;
use App\Models\RequestLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RequestLogApiController extends Controller
{
    /**
     * @LRDresponses 200|400|401|403|404|422|500
     */
    public function index(Request $request)
    {
        $limit = $request->limit ?? 10;
        $perPage = min($request->per_page ?? 20, 100);
        $orderBy = $request->order_by ?? 'created_at';
        $orderDir = $request->order_dir ?? 'desc';
        $offset = $request->offest;
        $data = RequestLog::with(['type', 'status'])
            ->filter($request->only(['search', 'date_range', 'personal_access_token_id', 'status_id', 'endpoint_id', 'status_code']))
            ->where('account_id', Auth::user()->account_id)
            ->orderBy($orderBy, $orderDir)
            ->paginate($perPage);
        //transform the data here
        return response()->json($data);
    }
}
