<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\RequestLogStatusEnum;
use App\Enums\StatusEnum;
use App\Enums\EndpointEnum;
use App\Enums\SubmissionStatusEnum;
use App\Events\SubmissionRequestCreatedEvent;
use App\Http\Requests\AnnualReturnAfsRequest;
use App\Models\RequestLog;
use App\Models\Submission;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class AnnualReturnAfsController extends CipcController
{
    /**
     * Annual Return AFS Filing
     *
     * This endpoint allows you to submit annual returns along with Annual Financial Statements (AFS) to CIPC via eServices.
     *
     * Functionality
     * - Submits XBRL format financial statements as part of the annual return filing.
     * - Ensures compliance with CIPC financial reporting requirements.
     * - Updates the company's compliance status based on the submitted financial details.
     *
     * Important Considerations
     * - Financial statements must be formatted in XBRL as per CIPC guidelines.
     * - Ensure accurate financial data to avoid rejection or compliance issues.
     *
     * Triggers an [Annual Return AFS Filing](#webhooks-POSTyour-webhook-url%23annual-return-afs-filing) webhook if successful.
     *
     * @group CIPC Endpoints
     *
     * @response 201 {
     *   "message": "Annual return AFS filing request has been received.",
     *   "submission_id": 123,
     *   "status": "SCHEDULED"
     * }
     *
     * @response 403 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "ERROR: Invalid enterprise number."
     *   }
     */
    public function index(AnnualReturnAfsRequest $request)
    {
        try {
            $requestLog = RequestLog::findOrFail($request->attributes->get('request_log_id'));
            $submission = Submission::where('reference', $requestLog->reference)
                ->where('account_id', $requestLog->account_id)
                ->where('endpoint_id', $requestLog->endpoint_id)
                ->latest()
                ->first();
            if ($submission && $submission->status === SubmissionStatusEnum::PROCESSING) {
                return response()->json([
                    'message' => 'A submission with the same reference is already being processed.',
                    'submission_id' => $submission->id,
                    'status' => SubmissionStatusEnum::PROCESSING->value,
                ], ResponseAlias::HTTP_CONFLICT);
            }
            $submission = Submission::updateOrCreate([
                'reference' => $requestLog->reference,
                'account_id' => $requestLog->account_id,
                'endpoint_id' => $requestLog->endpoint_id,
            ], [
                'status' => SubmissionStatusEnum::PENDING->value,
                'request_log_id' => $requestLog->id,
                'json_data' => $requestLog->request_body,
                'is_test' => $requestLog->is_test,
            ]);

            return response()->json([
                'message' => 'Annual return AFS request has been received.',
                'submission_id' => $submission->id,
                'status' => SubmissionStatusEnum::PENDING->value,
            ], ResponseAlias::HTTP_CREATED);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'An error occurred while processing your request.'
            ], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
