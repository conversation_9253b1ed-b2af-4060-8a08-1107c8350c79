<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\RequestLogStatusEnum;
use App\Enums\SubmissionStatusEnum;
use App\Http\Requests\DirectorChangeRequest;
use App\Models\RequestLog;
use App\Models\Submission;
use DateTime;
use Exception;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class DirectorChangeController extends CipcController
{
    /**
     * Director Change
     *
     * This endpoint allows you to update a company's directors via CIPC's new eServices.
     *
     * Functionality
     * - Supports director appointments, resignations, removals, and deceased status updates.
     * - Allows multiple directors to be appointed or resigned in a single transaction.
     * - Ensures director contact details are accurate and up-to-date for OTP verification.
     *
     * Triggers a [Director Change](#webhooks-POSTyour-webhook-url%23director-change) webhook if successful.
     *
     * @group CIPC Endpoints
     *
     * @response 201 {
     *   "message": "Director Change filing request has been received.",
     *   "submission_id": 123,
     *   "status": "SCHEDULED"
     * }
     *
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *   }
     * }
     */
    public function index(DirectorChangeRequest $request)
    {
        try {
            $requestLog = RequestLog::findOrFail($request->attributes->get('request_log_id'));

            // Check if this is a verify request
            if ($this->isVerifyRequest($request)) {
                // Handle verify request like DirectorChangeVerifyController
                $reference = $requestLog->reference;
                if (empty($requestLog->reference)) {
                    $reference = $requestLog->request_body['cipc_transaction']['id'];
                }

                $submission = Submission::create([
                    'request_log_id' => $requestLog->id,
                    'endpoint_id' => $requestLog->endpoint_id,
                    'reference' => $reference,
                    'account_id' => $requestLog->account_id,
                    'json_data' => $requestLog->request_body,
                    'is_test' => $requestLog->is_test,
                ]);

                return response()->json([
                    'message' => 'Director change request has been received.',
                    'submission_id' => $submission->id,
                    'status' => SubmissionStatusEnum::PENDING->value,
                ], ResponseAlias::HTTP_CREATED);
            }

            // Handle regular director change request
            $submission = Submission::where('reference', $requestLog->reference)
                ->where('account_id', $requestLog->account_id)
                ->where('endpoint_id', $requestLog->endpoint_id)
                ->latest()
                ->first();
            if ($submission && $submission->status === SubmissionStatusEnum::PROCESSING) {
                return response()->json([
                    'message' => 'A submission with the same reference is already being processed.',
                    'submission_id' => $submission->id,
                    'status' => SubmissionStatusEnum::PROCESSING->value,
                ], ResponseAlias::HTTP_CONFLICT);
            }
            $submission = Submission::updateOrCreate([
                'reference' => $requestLog->reference,
                'account_id' => $requestLog->account_id,
                'endpoint_id' => $requestLog->endpoint_id,
            ], [
                'status' => SubmissionStatusEnum::PENDING->value,
                'request_log_id' => $requestLog->id,
                'json_data' => $requestLog->request_body,
                'is_test' => $requestLog->is_test,
            ]);

            return response()->json([
                'message' => 'Director change request has been received.',
                'submission_id' => $submission->id,
                'status' => SubmissionStatusEnum::PENDING->value,
            ], ResponseAlias::HTTP_CREATED);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'An error occurred while processing your request.'
            ], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Check if this is a verify request by looking for OTPs or tracking number
     */
    private function isVerifyRequest($request): bool
    {
        $data = $request->all();

        // Check for verify stage data structure
        if (isset($data['cipc_transaction']) && isset($data['cipc_transaction']['tracking_no'])) {
            return true;
        } elseif (isset($data['directors']) && isset($data['directors'][0]['otps'])) {
            return true;
        }
        return false;
    }
}
