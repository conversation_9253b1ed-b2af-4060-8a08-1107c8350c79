<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\RequestLogStatusEnum;
use App\Enums\SubmissionStatusEnum;
use App\Http\Requests\DocumentRequest;
use App\Models\RequestLog;
use App\Models\Submission;
use Exception;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class DocumentRequestController extends CipcController
{
    /**
     * Document Request
     *
     * This endpoint allows you to submit a request for company-related documents via CIPC eServices.
     *
     * Functionality
     * - Request a Memorandum of Incorporation (MOI).
     * - Request a Disclosure Certificate.
     *
     * Important Considerations
     * - Ensure the request complies with CIPC document retrieval guidelines.
     * - Processing times may vary depending on the document type and CIPC queue.
     *
     * Triggers a [Document Request](#webhooks-POSTyour-webhook-url%23document-request) webhook if successful.
     *
     * @group CIPC Endpoints
     *
     * @response 201 {
     *   "message": "Document request has been received.",
     *   "submission_id": 123,
     *   "status": "SCHEDULED"
     * }
     *
     * @response 403 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "ERROR: Invalid enterprise number.",
     *   }
     * }
     */
    public function index(DocumentRequest $request)
    {
        try {
            $requestLog = RequestLog::findOrFail($request->attributes->get('request_log_id'));
            $submission = Submission::where('reference', $requestLog->reference)
                ->where('account_id', $requestLog->account_id)
                ->where('endpoint_id', $requestLog->endpoint_id)
                ->latest()
                ->first();
            if ($submission && $submission->status === SubmissionStatusEnum::PROCESSING) {
                return response()->json([
                    'message' => 'A submission with the same reference is already being processed.',
                    'submission_id' => $submission->id,
                    'status' => SubmissionStatusEnum::PROCESSING->value,
                ], ResponseAlias::HTTP_CONFLICT);
            }
            $submission = Submission::updateOrCreate([
                'reference' => $requestLog->reference,
                'account_id' => $requestLog->account_id,
                'endpoint_id' => $requestLog->endpoint_id,
            ], [
                'status' => SubmissionStatusEnum::PENDING->value,
                'request_log_id' => $requestLog->id,
                'json_data' => $requestLog->request_body,
                'is_test' => $requestLog->is_test,
            ]);

            return response()->json([
                'message' => 'Document request has been received.',
                'submission_id' => $submission->id,
                'status' => SubmissionStatusEnum::PENDING->value,
            ], ResponseAlias::HTTP_CREATED);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'An error occurred while processing your request.'
            ], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
