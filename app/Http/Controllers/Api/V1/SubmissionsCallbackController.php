<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\EndpointEnum;
use App\Enums\SubmissionStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\CipcOtpRequest;
use App\Http\Requests\SubmissionsCallbackRequest;
use App\Jobs\SendToLambdasJob;
use App\Models\CipcOtp;
use App\Models\RequestLog;
use App\Models\Submission;
use App\Models\SubmissionLog;
use App\Models\CipcCustomer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class SubmissionsCallbackController extends Controller
{


    public function handle(SubmissionsCallbackRequest $request): JsonResponse
    {
        $submission = Submission::where('reference', $request->transactionId)->latest()->first();
        if (!$submission) {
            return response()->json('Transaction not found.', ResponseAlias::HTTP_NOT_FOUND);
        }
        $submissionLog = null;
        if (!empty($request->aws_request_id)&& $request->aws_request_id!=='N/A') {
            $submissionLog = SubmissionLog::where('aws_request_id', $request->aws_request_id)->first();
        }

        // If we already have a submission log with this aws_request_id, just update it without handling
        // The job has already processed it, so we just sync the callback data
        if ($submissionLog) {
            $submissionLog->update([
                'status_code' => $request->statusCode,
                'response_body' => $request->validated(),
                'duration_ms' => min($submission->submitted_at ? abs($submission->submitted_at->diffInMilliseconds(now())) : 0, 900000),
            ]);

            // No handler call here - job already handled it
        }
        // Only create new submission log if we don't have one and conditions are met
        elseif ($request->local || (!in_array($submission->status, [SubmissionStatusEnum::SUCCESS->value, SubmissionStatusEnum::PROCESSING->value]) || !in_array($submission->endpoint_id, EndpointEnum::getSDKTransactionTypes()))) {
            $submissionLog = $this->createSubmissionLog($submission, $request);
            $handler = EndpointEnum::getEndpointSubmissionLogHandler($submission->endpoint_id);
            $handler->handle($submissionLog);
        }
        return response()->json(['message' => 'Response received and will be processed'], ResponseAlias::HTTP_OK);
    }

    public function processBoOtp(CipcOtpRequest $request): JsonResponse
    {
        $trackingNumber = $request->trackingNumber;
        $otp = $request->otp;
        $emailOtp = !empty($otp) && $otp[0] === 'E';
        //find request log with tracking number
        $requestLog = RequestLog::where('tracking_no', $trackingNumber)->orderBy('id', 'desc')->first();
        if (!$requestLog) {
            return response()->json('Transaction not found.', ResponseAlias::HTTP_NOT_FOUND);
        }
        // Create the OTP record for the transaction (Both Email and SMS OTPs will be saved)
        CipcOtp::firstOrCreate([
            'request_log_id' => $requestLog->id,
            'otp' => $otp,
        ]);
        $submission = Submission::where('reference', $requestLog->reference)->where('status', '!=', SubmissionStatusEnum::SUCCESS->value)->first();
        if ($submission) {
            $submission->update([
                'status' => SubmissionStatusEnum::OTP_PENDING->value,
            ]);
            // To avoid triggering multiple initial submissions, we will trigger upon receiving the Email OTP, which usually follows the SMS OTP.
            if ($emailOtp) {
                SendToLambdasJob::dispatch($submission);
            }
        }
        return response()->json('Transaction processed.', ResponseAlias::HTTP_OK);
    }

    public function updateAuthUrl(Request $request): JsonResponse
    {
        $customerCode = $request->input('customerCode');
        $authUrl = $request->input('auth_url');
        $customer = CipcCustomer::where('code', $customerCode)->first();
        if ($customer) {
            $customer->update([
                'auth_url' => $authUrl,
            ]);
        }
        return response()->json(['message' => 'Auth URL updated.'], ResponseAlias::HTTP_OK);
    }

    /**
     * Create a new submission log
     */
    private function createSubmissionLog(Submission $submission, SubmissionsCallbackRequest $request): SubmissionLog
    {
        $submissionLog = new SubmissionLog();
        $submissionLog->submission_id = $submission->id;
        $submissionLog->status_code = $request->statusCode;
        $submissionLog->status = SubmissionStatusEnum::PROCESSING->value;
        $submissionLog->payload = [];
        $submissionLog->function_name = $submission->endpoint->function_name;
        $submissionLog->url = $submission->endpoint->url_path;
        $submissionLog->response_body = $request->validated();
        $submissionLog->aws_request_id = $request->aws_request_id;
        $submissionLog->duration_ms = min($submission->submitted_at ? abs($submission->submitted_at->diffInMilliseconds(now())) : 0, 900000);
        $submissionLog->save();

        return $submissionLog;
    }
}
