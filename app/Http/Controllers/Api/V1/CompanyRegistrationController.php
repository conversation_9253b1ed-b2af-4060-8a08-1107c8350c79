<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\RequestLogStatusEnum;
use App\Enums\SubmissionStatusEnum;
use App\Http\Requests\CompanyRegistrationRequest;
use App\Models\RequestLog;
use App\Models\Submission;
use Exception;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Illuminate\Foundation\Http\FormRequest;
use <PERSON>nuckles\Scribe\Attributes\Response;

#[Response(status: 201, description: 'Company registration request has been received.', content: [
    'message' => 'Company registration request has been received.',
    'submission_id' => 123,
    'status' => 'SCHEDULED'
])]
#[Response(status: 422, description: 'Validation error', content: [
    'message' => 'The given data was invalid.',
    'errors' => []
])]
class CompanyRegistrationController extends CipcController
{
    /**
     * New Company Registration
     *
     * This endpoint allows you to register a Private Company (PTY LTD) with a standard Memorandum of Incorporation (MOI) through CIPC eServices.
     *
     * The registration process includes:
     * - Name Reservation: Submit up to four proposed name options. Ensure each name is unique and distinct from your alternative choices.
     * - Authorised Shares: Specify the total number of shares available in the company before they are issued to shareholders.
     *
     * Triggers a [Company Registration](#webhooks-POSTyour-webhook-url%23new-company-registration) webhook if successful.
     *
     * @group CIPC Endpoints
     */
    public function index(CompanyRegistrationRequest $request)
    {

        try {
            $requestLog = RequestLog::findOrFail($request->attributes->get('request_log_id'));
            $submission = Submission::where('reference', $requestLog->reference)
                ->where('account_id', $requestLog->account_id)
                ->where('endpoint_id', $requestLog->endpoint_id)
                ->latest()
                ->first();
            if ($submission && $submission->status === SubmissionStatusEnum::PROCESSING) {
                return response()->json([
                    'message' => 'A submission with the same reference is already being processed.',
                    'submission_id' => $submission->id,
                    'status' => SubmissionStatusEnum::PROCESSING->value,
                ], ResponseAlias::HTTP_CONFLICT);
            }
            $submission = Submission::updateOrCreate([
                'reference' => $requestLog->reference,
                'account_id' => $requestLog->account_id,
                'endpoint_id' => $requestLog->endpoint_id,
            ], [
                'status' => SubmissionStatusEnum::PENDING->value,
                'request_log_id' => $requestLog->id,
                'json_data' => $requestLog->request_body,
                'is_test' => $requestLog->is_test,
            ]);
            return response()->json([
                'message' => 'Company registration request has been received.',
                'submission_id' => $submission->id,
                'status' => SubmissionStatusEnum::PENDING->value,
            ], ResponseAlias::HTTP_CREATED);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'An error occurred while processing your request.'
            ], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
