<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\RequestLogStatusEnum;
use App\Enums\SubmissionStatusEnum;
use App\Http\Requests\DirectorChangeVerifyRequest;
use App\Models\RequestLog;
use App\Models\Submission;
use Exception;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class DirectorChangeVerifyController extends CipcController
{
    /**
     * Director Change Verification
     *
     * This endpoint allows you to verify a director's One-Time Password (OTP) with CIPC via the new eServices platform.
     *
     * Functionality
     * - Submits an OTP verification request for a director.
     * - Ensures secure authentication before processing director changes.
     *
     * Important Considerations
     * - The director's contact information must be up to date to receive the OTP.
     * - OTP verification is required to authorize director appointments, resignations, or other changes.
     *
     * @group CIPC Endpoints
     *
     * @response 201 {
     *   "message": "Director change verification request has been received.",
     *   "submission_id": 123,
     *   "status": "SCHEDULED"
     * }
     *
     * @response 422 {
     *   "message": "The given data was invalid.",
     */
    public function index(DirectorChangeVerifyRequest $request)
    {
        //todo: find a way to handle existing director change request and this verify request
        try {
            $requestLog = RequestLog::findOrFail($request->attributes->get('request_log_id'));
            //todo:force infodocs to send reference for director change verify
            $reference = $requestLog->reference;
            if (empty($requestLog->reference)) {
                $reference = $requestLog->request_body['cipc_transaction']['id'];
            }
            $submission = Submission::create([
                'request_log_id' => $requestLog->id,
                'endpoint_id' => $requestLog->endpoint_id,
                'reference' => $reference,
                'account_id' => $requestLog->account_id,
                'json_data' => $requestLog->request_body,
                'is_test' => $requestLog->is_test,
            ]);

            return response()->json([
                'message' => 'Director change request has been received.',
                'submission_id' => $submission->id,
                'status' => SubmissionStatusEnum::PENDING->value,
            ], ResponseAlias::HTTP_CREATED);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'An error occurred while processing your request.',
            ], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
