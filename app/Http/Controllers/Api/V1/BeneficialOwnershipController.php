<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\RequestLogStatusEnum;
use App\Enums\SubmissionStatusEnum;
use App\Http\Requests\BeneficialOwnershipRequest;
use App\Models\RequestLog;
use App\Models\Submission;
use Exception;

use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class BeneficialOwnershipController extends CipcController
{
    /**
     * Beneficial Ownership Filing
     *
     * This endpoint allows you to submit beneficial ownership information, along with supporting documents, to CIPC via the new eServices platform.
     *
     * Requirements
     * - Beneficial Ownership Details: Submit ownership information in compliance with regulatory guidelines.
     * - Supporting Documents: Upload necessary documentation to verify ownership.
     * - Foreigner Assurance: Any foreign beneficial owner must be verified on CIPC before they can be disclosed as a beneficial owner of a company.
     *
     * Triggers a [Beneficial Ownership Filing](#webhooks-POSTyour-webhook-url%23beneficial-ownership-filing) webhook if successful.
     *
     * @group CIPC Endpoints
     *
     * @response 201 {
     *   "message": "Beneficial ownership filing request has been received.",
     *   "submission_id": 123,
     *   "status": "SCHEDULED"
     * }
     *
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "query_data.owners": ["Total ownership must be between 5 and 100."]
     *   }
     * }
     */
    public function index(BeneficialOwnershipRequest $request)
    {
        try {
            $requestLog = RequestLog::findOrFail($request->attributes->get('request_log_id'));
            $submission = Submission::where('reference', $requestLog->reference)
                ->where('account_id', $requestLog->account_id)
                ->where('endpoint_id', $requestLog->endpoint_id)
                ->latest()
                ->first();
            if ($submission && $submission->status === SubmissionStatusEnum::PROCESSING) {
                return response()->json([
                    'message' => 'A submission with the same reference is already being processed.',
                    'submission_id' => $submission->id,
                    'status' => SubmissionStatusEnum::PROCESSING->value,
                ], ResponseAlias::HTTP_CONFLICT);
            }
            $submission = Submission::updateOrCreate([
                'reference' => $requestLog->reference,
                'account_id' => $requestLog->account_id,
                'endpoint_id' => $requestLog->endpoint_id,
            ], [
                'status' => SubmissionStatusEnum::PENDING->value,
                'request_log_id' => $requestLog->id,
                'json_data' => $requestLog->request_body,
                'is_test' => $requestLog->is_test,
            ]);

            return response()->json([
                'message' => 'Beneficial ownership filing request has been received.',
                'submission_id' => $submission->id,
                'status' => SubmissionStatusEnum::PENDING->value,
            ], ResponseAlias::HTTP_CREATED);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'An error occurred while processing your request.'
            ], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
