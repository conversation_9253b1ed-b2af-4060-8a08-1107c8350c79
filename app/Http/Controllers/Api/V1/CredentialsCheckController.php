<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\SubmissionStatusEnum;
use App\Http\Requests\CredentialsCheckRequest;
use App\Models\RequestLog;
use App\Models\Submission;
use App\Models\SubmissionLog;
use Aws\Lambda\LambdaClient;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Response;
use Psr\Http\Message\StreamInterface;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class CredentialsCheckController extends CipcController
{
    /**
     * Credentials Checker
     *
     * This endpoint allows you to check if your credentials are valid for logging into CIPC
     * @group CIPC Endpoints
     *
     * @response 200 {
     *   "message": "Successfully logged in",
     * }
     *
     * @response 403 {
     *   "message": "Captcha failed. Please try again.",
     * }
     * @response 400 {
     *   "message": "incorrect password, incorrect customer code, etc",
     * }
     */

    public function index(CredentialsCheckRequest $request)
    {
        ini_set('max_execution_time', 100);
        try {
            $requestLog = RequestLog::findOrFail($request->attributes->get('request_log_id'));
            $submission = Submission::create([
                'request_log_id' => $requestLog->id,
                'endpoint_id' => $requestLog->endpoint_id,
                'reference' => $requestLog->reference,
                'account_id' => $requestLog->account_id,
                'json_data' => $requestLog->request_body,
                'is_test' => $requestLog->is_test,
                'status' => SubmissionStatusEnum::PROCESSING->value,
            ]);
            $response = $this->sendToLambda($submission);
            $this->updateSubmissionStatus($submission, $response['statusCode']);
            return response()->json($response, ResponseAlias::HTTP_OK);
        } catch (Exception $e) {
            return response()->json($e, ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function sendToLambda($submission)
    {
        $client = app(LambdaClient::class);
        $payload = [
            'body' => ['Customer_Code' => $submission->json_data['customer_code'], 'Customer_Password' => $submission->json_data['customer_password']]
        ];
        $functionName = 'LoginCredentialsVerify';
        //create submission log
        $submissionLog = SubmissionLog::create([
            'submission_id' => $submission->id,
            'status' => SubmissionStatusEnum::PROCESSING->value,
            'response_body' => [],
            'function_name' => $functionName,
            'payload' => $payload,
        ]);
        $startTime = Carbon::now();
        try {
            $result = $client->invoke([
                'FunctionName' => $functionName,
                'InvocationType' => 'RequestResponse',
                'Payload' => json_encode($payload),
            ]);
            $payload = $result->get('Payload');
            if ($payload instanceof StreamInterface) {
                $response = json_decode($payload->getContents(), true);
            } else {
                $response = json_decode($payload, true);
            }
        } catch (Exception $e) {
            $response = [
                'statusCode' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'body' => [
                    'Response' => $e->getMessage()
                ],
                'isBase64Encoded' => false,
            ];
        }
        if (empty($response)) {
            $response = [
                'statusCode' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'body' => [
                    'Response' => 'Internal Server Error'
                ],
                'isBase64Encoded' => false,
            ];
        }
        // Capture AWS request ID sync invocations
        $awsRequestId = $result['@metadata']['headers']['x-amzn-requestid'] ?? null;
        $submissionLog->update([
            'aws_request_id' => $awsRequestId,
            'response_body' => $response,
            'status_code' => $response['statusCode'],
            'duration_ms' => abs(Carbon::now()->diffInMilliseconds($startTime)),
        ]);
        $this->updateSubmissionLogStatus($submissionLog, $response['statusCode']);
        return $response;
    }

    /**
     * Update submission status based on response status code
     *
     * @param Submission $submission
     * @param int $statusCode
     * @return void
     */
    private function updateSubmissionStatus(Submission $submission, int $statusCode): void
    {
        $status = $statusCode === Response::HTTP_OK
            ? SubmissionStatusEnum::SUCCESS->value
            : SubmissionStatusEnum::FAILED->value;

        $submission->update(['status' => $status]);
    }

    /**
     * Update submission log status based on response status code
     *
     * @param SubmissionLog $submissionLog
     * @param int $statusCode
     * @return void
     */
    private function updateSubmissionLogStatus(SubmissionLog $submissionLog, int $statusCode): void
    {
        $status = $statusCode === Response::HTTP_OK
            ? SubmissionStatusEnum::SUCCESS->value
            : SubmissionStatusEnum::FAILED->value;

        $submissionLog->update(['status' => $status]);
    }
}
