<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\RequestLogStatusEnum;
use App\Enums\SubmissionStatusEnum;
use App\Enums\EndpointEnum;
use App\Events\SubmissionRequestCreatedEvent;
use App\Http\Requests\AddressChangeRequest;
use App\Models\RequestLog;
use App\Models\Submission;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class AddressChangeController extends CipcController
{
    /**
     * Address Change
     *
     * This endpoint allows you to submit an address change for a company to CIPC via the new eServices platform.
     *
     * Applicable Entities
     * - Private Companies (PTYs)
     * - Close Corporations (CCs)
     * - Incorporated Companies (INC)
     * - Non-Profit Companies (NPCs)
     *
     * Ensure the address follows the correct South African Address Format:
     *
     * **Line 1:** Street Address – Includes building number, street name, and any apartment or unit number. <br>
     * **Line 2:** Additional Address Information – Extension of the street address, if applicable. <br>
     * **Line 3:** City/Town/Suburb – The name of the city or town where the property is located. <br>
     * **Line 4:** Province/State/Region – The region, state, or province where the city is located. <br>
     * **Line 5:** Postal Code/ZIP Code – The relevant postal code or ZIP code.<br>
     *
     * Triggers an [Address Change](#webhooks-POSTyour-webhook-url%23address-change) webhook if successful.
     *
     * @group CIPC Endpoints
     *
     * @response 201 {
     *   "message": "Address change request has been received.",
     *   "submission_id": 123,
     *   "status": "SCHEDULED"
     * }
     *
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "query_data.physical_address_2": ["The physical address 2 field is required."]
     *   }
     * }
     */

    public function index(AddressChangeRequest $request)
    {
        try {
            $requestLog = RequestLog::findOrFail($request->attributes->get('request_log_id'));
            $submission = Submission::where('reference', $requestLog->reference)
                ->where('account_id', $requestLog->account_id)
                ->where('endpoint_id', $requestLog->endpoint_id)
                ->latest()
                ->first();
            if ($submission && $submission->status === SubmissionStatusEnum::PROCESSING) {
                return response()->json([
                    'message' => 'A submission with the same reference is already being processed.',
                    'submission_id' => $submission->id,
                    'status' => SubmissionStatusEnum::PROCESSING->value,
                ], ResponseAlias::HTTP_CONFLICT);
            }
            $submission = Submission::updateOrCreate([
                'reference' => $requestLog->reference,
                'account_id' => $requestLog->account_id,
                'endpoint_id' => $requestLog->endpoint_id,
            ], [
                'status' => SubmissionStatusEnum::PENDING->value,
                'request_log_id' => $requestLog->id,
                'json_data' => $requestLog->request_body,
                'is_test' => $requestLog->is_test,
            ]);


            return response()->json([
                'message' => 'Address change request has been received.',
                'submission_id' => $submission->id,
                'status' => SubmissionStatusEnum::PENDING->value,
            ], ResponseAlias::HTTP_CREATED);
        } catch (Exception $e) {
            return response()->json($e, ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

}
