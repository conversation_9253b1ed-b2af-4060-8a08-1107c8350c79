<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\RequestLogStatusEnum;
use App\Enums\SubmissionStatusEnum;
use App\Http\Requests\BeneficialOwnershipVerifyRequest;
use App\Models\RequestLog;
use App\Models\Submission;
use Exception;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class BeneficialOwnershipVerifyController extends CipcController
{
    /**
     * Beneficial Ownership Verification
     *
     * This endpoint allows you to verify a beneficial owner's One-Time Password (OTP) with CIPC via eServices.
     *
     * Functionality
     * - Submits an OTP verification request for a beneficial ownership submission.
     * - Ensures secure authentication before processing beneficial ownership disclosures.
     *
     * Important Considerations
     * - OTP verification is required to authorize beneficial ownership filings and updates.
     * - OTPs are sent to the contact details as per the customer code. These need to be up to date to ensure successful verification.
     * - The verification process must be completed within a specific time window after the OTP is sent.
     *
     * @group CIPC Endpoints
     *
     * @response 201 {
     *   "message": "Beneficial ownership verification request has been received.",
     *   "submission_id": 123,
     *   "status": "OTP_PENDING"
     * }
     *
     * @response 403 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "ERROR: Invalid tracking number."
     *   }
     * }
     */
    public function index(BeneficialOwnershipVerifyRequest $request)
    {
        try {
            $requestLog = RequestLog::findOrFail($request->attributes->get('request_log_id'));
            $latestSubmission = Submission::where([
                'reference' => $requestLog->reference,
                'endpoint_id' => $requestLog->endpoint_id,
                'account_id' => $requestLog->account_id,
            ])
                ->latest('created_at') // Get the most recent record
                ->first();

            if ($latestSubmission) {
                // Update the latest record
                $latestSubmission->update([
                    'request_log_id' => $requestLog->id,
                    'status' => SubmissionStatusEnum::OTP_PENDING->value,
                    'json_data' => $requestLog->request_body,
                    'is_test' => $requestLog->is_test,
                    'retry_count' => 0,
                ]);
                $submission = $latestSubmission;
            } else {
                // Create a new record if none exists
                $submission = Submission::create([
                    'reference' => $requestLog->reference,
                    'endpoint_id' => $requestLog->endpoint_id,
                    'account_id' => $requestLog->account_id,
                    'request_log_id' => $requestLog->id,
                    'status' => SubmissionStatusEnum::OTP_PENDING->value,
                    'json_data' => $requestLog->request_body,
                    'is_test' => $requestLog->is_test,
                ]);
            }


            return response()->json([
                'message' => 'Beneficial ownership verification request has been received.',
                'submission_id' => $submission->id,
                'status' => SubmissionStatusEnum::PENDING->value,
            ], ResponseAlias::HTTP_CREATED);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'An error occurred while processing your request.'
            ], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
