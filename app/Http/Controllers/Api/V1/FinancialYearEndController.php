<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\RequestLogStatusEnum;
use App\Enums\SubmissionStatusEnum;
use App\Http\Requests\FinancialYearEndRequest;
use App\Models\RequestLog;
use App\Models\Submission;
use Exception;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class FinancialYearEndController extends CipcController
{
    /**
     * Financial Year-End Change
     *
     * This endpoint allows you to change a company’s financial year-end through CIPC eServices.
     *
     * Requirements
     * - Company Registration Number: Required for identification.
     * - New Financial Year-End Date: Must comply with CIPC guidelines.
     *
     * Important Considerations
     * - The company must have an "In Business" status to proceed.
     * - Only certain companies are eligible for a financial year-end change based on regulatory conditions.
     * - Ensure all company details are accurate before submission to avoid delays or rejection.
     *
     * Triggers a [Financial Year-End Change](#webhooks-POSTyour-webhook-url%23financial-year-end-change) webhook if successful.
     *
     * @group CIPC Endpoints
     *
     * @response 201 {
     *   "message": "Financial year end request has been received.",
     *   "submission_id": 123,
     *   "status": "SCHEDULED"
     * }
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *   }
     * }
     */
    public function index(FinancialYearEndRequest $request)
    {
        try {
            $requestLog = RequestLog::findOrFail($request->attributes->get('request_log_id'));
            $submission = Submission::where('reference', $requestLog->reference)
                ->where('account_id', $requestLog->account_id)
                ->where('endpoint_id', $requestLog->endpoint_id)
                ->latest()
                ->first();
            if ($submission && $submission->status === SubmissionStatusEnum::PROCESSING) {
                return response()->json([
                    'message' => 'A submission with the same reference is already being processed.',
                    'submission_id' => $submission->id,
                    'status' => SubmissionStatusEnum::PROCESSING->value,
                ], ResponseAlias::HTTP_CONFLICT);
            }
            $submission = Submission::updateOrCreate([
                'reference' => $requestLog->reference,
                'account_id' => $requestLog->account_id,
                'endpoint_id' => $requestLog->endpoint_id,
            ], [
                'status' => SubmissionStatusEnum::PENDING->value,
                'request_log_id' => $requestLog->id,
                'json_data' => $requestLog->request_body,
                'is_test' => $requestLog->is_test,
            ]);

            return response()->json([
                'message' => 'Financial year end request has been received.',
                'submission_id' => $submission->id,
                'status' => SubmissionStatusEnum::PENDING->value,
            ], ResponseAlias::HTTP_CREATED);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'An error occurred while processing your request.'
            ], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
