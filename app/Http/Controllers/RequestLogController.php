<?php

namespace App\Http\Controllers;

use App\Models\RequestLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class RequestLogController extends Controller
{
    public function index(Request $request)
    {
        $results = RequestLog::with(['endpoint', 'status'])
            ->filter($request->only(['search', 'date_range', 'personal_access_token_id', 'status_id', 'endpoint_id', 'account_id', 'status_code']))
            ->where('account_id', Auth::user()->account_id)
            ->orderBy('created_at', 'desc')
            ->paginate();
        return Inertia::render('RequestLogs/Index', [
            'results' => $results,
            'filters' => $request->only(['search', 'date_range', 'personal_access_token_id', 'status_id', 'endpoint_id', 'account_id', 'status_code'])
        ]);
    }

    public function show(RequestLog $log)
    {
        if ($log->account_id !== Auth::user()->account_id) {
            abort(404);
        }
        $log->load(['type', 'status', 'responseLogs']);
        return Inertia::render('RequestLogs/Show', [
            'log' => $log,
        ]);
    }
}
