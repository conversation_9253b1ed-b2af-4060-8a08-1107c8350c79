<?php

namespace App\Http\Controllers;

use App\Http\Requests\ForeignerAssuranceVerifyRequest;
use App\Services\ForeignerAssuranceService;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class ForeignerAssuranceVerifyController extends Controller
{
    protected ForeignerAssuranceService $foreignerAssuranceService;
    public function __construct(ForeignerAssuranceService $foreignerAssuranceService)
    {
        $this->foreignerAssuranceService = $foreignerAssuranceService;
    }

    /**
     * Verify Foreigner Assurance
     *
     * Check the foreigner assurance status of a passport number via CIPC new eServices.
     *
     * @group CIPC Endpoints
     *
     * @response 200 {
     *   "error": false,
     *   "duration": "12.34 seconds",
     *   "applications": [
     *     {
     *       "passport_number": "1234567890",
     *       "status": "Approved",
     *       "date": "2025/01/01 10:24:36 AM"
     *     },
     *     {
     *       "passport_number": "1234567890",
     *       "status": "Rejected",
     *       "date": "2025/01/01 10:24:36 AM"
     *     },
     *     {
     *       "passport_number": "1234567890",
     *       "status": "Inprogress",
     *     },
     *     {
     *       "passport_number": "1234567890",
     *       "status": "Not found",
     *     }
     *   ]
     * }
     */
    public function index(ForeignerAssuranceVerifyRequest $request)
    {
        try {
            $params = $request->all();
            $verifyResult = $this->foreignerAssuranceService->verify($params);
            return response()->json($verifyResult);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
