<?php

namespace App\Http\Requests;


use Illuminate\Foundation\Http\FormRequest;

/**
 * Class AddressChangeRequest
 *
 * @property string $reference
 * @property string $customer_code
 * @property string $customer_password
 * @property QueryData $query_data
 */
class AddressChangeRequest extends CipcBaseRequest
{


    public function authorize()
    {
        return true; // Adjust as necessary
    }

    public function rules()
    {
        return array_merge($this->getCipcRules(), [
            'reference' => 'required',
            'query_data' => 'required|array',
            'query_data.registration_number' => [
                'required',
                'string',
                'regex:/^[0-9]{4}\/\d{6}\/\d{2}$/',
                function ($attribute, $value, $fail) {
                    // Extract the last 2 digits
                    preg_match('/\d{2}$/', $value, $matches);
                    if (!empty($matches) && in_array($matches[0], ['24', '25', '26'])) {
                        $fail("The {$attribute} cannot end with 24, 25, or 26.");
                    }
                }
            ],
            'query_data.company_type_id' => 'required|integer',
            'query_data.email' => [
                'required',
                'email',
                function ($attribute, $value, $fail) {
                    if (str_ends_with($value, '.africa')) {
                        $fail('The email address cannot end with .africa');
                    }
                },
            ],
            'query_data.physical_address_1' => 'required|string',
            'query_data.physical_address_2' => 'nullable|string',
            'query_data.physical_city' => 'required|string',
            'query_data.physical_province' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    if (!preg_match("/^[A-Za-z\s-]+$/", $value)) {
                        $fail('The province can only contain letters, spaces, and hyphens');
                    }
                },
            ],
            'query_data.physical_postal_code' => 'required|string',
            'query_data.physical_country' => 'nullable|string',
            'query_data.pos_address_1' => 'required|string',
            'query_data.pos_address_2' => 'nullable|string',
            'query_data.pos_city' => 'required|string',
            'query_data.pos_province' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    if (!preg_match("/^[A-Za-z\s-]+$/", $value)) {
                        $fail('The province can only contain letters, spaces, and hyphens');
                    }
                },
            ],
            'query_data.pos_postal_code' => 'required|string',
        ]);
    }

    public function messages(): array
    {
        return [
            'reference.required' => 'The transaction ID is required.',
            'query_data.required' => 'The query data is required.',
            'query_data.array' => 'The query data must be an array.',

            'query_data.registration_number.required' => 'The registration number is required.',
            'query_data.registration_number.string' => 'The registration number must be text.',
            'query_data.registration_number.regex' => 'The registration number must be in the format YYYY/XXXXXX/ZZ.',

            'query_data.company_type_id.required' => 'The company type ID is required.',
            'query_data.company_type_id.integer' => 'The company type ID must be a number.',

            'query_data.email.required' => 'The company email is required.',
            'query_data.email.email' => 'Please provide a valid email address.',

            'query_data.physical_address_1.required' => 'The physical address line 1 is required.',
            'query_data.physical_address_1.string' => 'The physical address line 1 must be text.',
            'query_data.physical_address_2.string' => 'The physical address line 2 must be text.',
            'query_data.physical_city.required' => 'The physical address city is required.',
            'query_data.physical_city.string' => 'The physical address city must be text.',
            'query_data.physical_province.required' => 'The physical address province is required.',
            'query_data.physical_province.string' => 'The physical address province must be text.',
            'query_data.physical_postal_code.required' => 'The physical address postal code is required.',
            'query_data.physical_postal_code.string' => 'The physical address postal code must be text.',
            'query_data.physical_country.string' => 'The physical address country must be text. Currently it will default to South Africa.',

            'query_data.pos_address_1.required' => 'The postal address line 1 is required.',
            'query_data.pos_address_1.string' => 'The postal address line 1 must be text.',
            'query_data.pos_address_2.string' => 'The postal address line 2 must be text.',
            'query_data.pos_city.required' => 'The postal address city is required.',
            'query_data.pos_city.string' => 'The postal address city must be text.',
            'query_data.pos_province.required' => 'The postal address province is required.',
            'query_data.pos_province.string' => 'The postal address province must be text.',
            'query_data.pos_postal_code.required' => 'The postal address postal code is required.',
            'query_data.pos_postal_code.string' => 'The postal address postal code must be text.',
        ];
    }

    public function bodyParameters(): array
    {
        return [
            'reference' => [
                'description' => 'The transaction ID for this request',
                'example' => '12345',
                'required' => true,
                'type' => 'string'
            ],
            'customer_code' => [
                'description' => 'The customer code for authentication',
                'example' => 'CUST001',
                'required' => true,
                'type' => 'string'
            ],
            'customer_password' => [
                'description' => 'The customer password for authentication',
                'example' => 'password123',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.registration_number' => [
                'description' => 'The company registration number in format YYYY/XXXXXX/ZZ',
                'example' => '2020/123456/07',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.company_type_id' => [
                'description' => 'The company type identifier',
                'example' => 1,
                'required' => true,
                'type' => 'integer'
            ],
            'query_data.email' => [
                'description' => 'Company email address (cannot end with .africa)',
                'example' => '<EMAIL>',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.physical_address_1' => [
                'description' => 'Physical address line 1',
                'example' => '26 Roseberry Avenue',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.physical_address_2' => [
                'description' => 'Physical address line 2',
                'example' => 'Oranjezicht',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.physical_city' => [
                'description' => 'Physical address city',
                'example' => 'Cape Town',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.physical_province' => [
                'description' => 'Physical address province (letters, spaces, and hyphens only)',
                'example' => 'Western Cape',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.physical_postal_code' => [
                'description' => 'Physical address postal code',
                'example' => '8001',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.physical_country' => [
                'description' => 'Physical address country (defaults to South Africa if not provided)',
                'example' => 'South Africa',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.pos_address_1' => [
                'description' => 'Postal address line 1',
                'example' => '26 Roseberry Avenue',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.pos_address_2' => [
                'description' => 'Postal address line 2',
                'example' => 'Oranjezicht',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.pos_city' => [
                'description' => 'Postal address city',
                'example' => 'Cape Town',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.pos_province' => [
                'description' => 'Postal address province (letters, spaces, and hyphens only)',
                'example' => 'Western Cape',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.pos_postal_code' => [
                'description' => 'Postal address postal code',
                'example' => '8001',
                'required' => true,
                'type' => 'string'
            ]
        ];
    }
}
