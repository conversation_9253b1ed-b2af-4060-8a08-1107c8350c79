<?php

namespace App\Http\Requests;

use App\Enums\InterestTypeEnum;
use App\Enums\GenderEnum;
use App\Enums\ProvinceSouthAfricaEnum;
use App\Enums\RaceEnum;
use App\Rules\IntegerOrBooleanRule;

use Illuminate\Validation\Rules\Enum;

class BeneficialOwnershipRequest extends CipcCompanyBaseRequest
{
    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        $queryData = request('query_data', []);

        // Ensure 'owners' exists inside 'query_data'
        $queryData['owners'] = array_map(function ($owner) {
            $owner['owner_type'] = $owner['owner_type'] ?? 'Natural';
            return $owner;
        }, $queryData['owners'] ?? []);

        // Merge the updated 'query_data' back into the request
        $this->merge([
            'query_data' => $queryData
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return array_merge($this->getCompanyRules(), [
            'query_data.owners' => [
                'present',
                'array',
                'min:1',
                function ($attribute, $value, $fail) {
                    $owners = $value;
                    $totalOwnership = 0;
                    $hasNaturalOwner = false;
                    $hasJuristicOwner = false;
                    foreach ($owners as $index => $owner) {
                        if ($owner['owner_type'] === 'Natural') {
                            $hasNaturalOwner = true;
                        } elseif ($owner['owner_type'] === 'Juristic') {
                            $hasJuristicOwner = true;
                        }

                        if (empty($owner['ownership']) && empty($owner['interest_types'])) {
                            $fail("Either ownership or interest types must be provided for owner {$index}.");
                        }

                        // Calculate total ownership from interest_types if present, otherwise use legacy ownership field
                        if (!empty($owner['interest_types'])) {
                            foreach ($owner['interest_types'] as $interest) {
                                if ($interest['name'] === InterestTypeEnum::SHAREHOLDING->value) {
                                    $totalOwnership += floatval($interest['value']);
                                }
                            }
                        } elseif (isset($owner['ownership'])) {
                            $totalOwnership += floatval($owner['ownership']);
                        }
                    }

                    if (!$hasNaturalOwner && $hasJuristicOwner) {
                        $fail("At least one owner must be of type Natural if there is a Juristic owner.");
                    }

                    if ($totalOwnership > 0 && ($totalOwnership > 100.0001)) {
                        $fail("Total ownership must not exceed 100%.");
                    }
                }
            ],

            // Owner Type
            'query_data.owners.*.owner_type' => ['required', 'string', 'in:Natural,Juristic'],

            // Natural Owner Fields
            'query_data.owners.*.first_name' => [
                'required_if:query_data.owners.*.owner_type,Natural',
                'string',
                'prohibited_unless:query_data.owners.*.owner_type,Natural'
            ],
            'query_data.owners.*.surname' => [
                'required_if:query_data.owners.*.owner_type,Natural',
                'string',
                'prohibited_unless:query_data.owners.*.owner_type,Natural'
            ],
            'query_data.owners.*.name' => [
                'required_if:query_data.owners.*.owner_type,Natural',
                'string',
                'prohibited_unless:query_data.owners.*.owner_type,Natural'
            ],
            'query_data.owners.*.id_number' => [
                'required_if:query_data.owners.*.owner_type,Natural',
                'string',
                'prohibited_unless:query_data.owners.*.owner_type,Natural'
            ],
            'query_data.owners.*.date_of_birth' => [
                'required_if:query_data.owners.*.owner_type,Natural',
                'date_format:Y-m-d',
                'prohibited_unless:query_data.owners.*.owner_type,Natural'
            ],
            'query_data.owners.*.date_issued' => [
                'required_if:query_data.owners.*.owner_type,Natural',
                'date_format:Y-m-d',
                'prohibited_unless:query_data.owners.*.owner_type,Natural'
            ],
            'query_data.owners.*.gender' => [
                'required_if:query_data.owners.*.owner_type,Natural',
                'string',
                new Enum(GenderEnum::class),
                'prohibited_unless:query_data.owners.*.owner_type,Natural'
            ],
            'query_data.owners.*.race' => [
                'required_if:query_data.owners.*.owner_type,Natural',
                'string',
                new Enum(RaceEnum::class),
                'prohibited_unless:query_data.owners.*.owner_type,Natural'
            ],
            'query_data.owners.*.citizen' => [
                'required_if:query_data.owners.*.owner_type,Natural',
                new IntegerOrBooleanRule(),
                'prohibited_unless:query_data.owners.*.owner_type,Natural'
            ],
            'query_data.owners.*.disability' => [
                'required_if:query_data.owners.*.owner_type,Natural',
                new IntegerOrBooleanRule(),
                'prohibited_unless:query_data.owners.*.owner_type,Natural'
            ],
            'query_data.owners.*.country_of_origin' => [
                'required_if:query_data.owners.*.owner_type,Natural',
                'string',
                'prohibited_unless:query_data.owners.*.owner_type,Natural'
            ],
            'query_data.owners.*.email' => [
                'required_if:query_data.owners.*.owner_type,Natural',
                'string',
                'prohibited_unless:query_data.owners.*.owner_type,Natural',
                function ($attribute, $value, $fail) {
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $fail('email field must be a valid email address.');
                    }
                }
            ],
            'query_data.owners.*.phone' => [
                'required_if:query_data.owners.*.owner_type,Natural',
                'string',
                'prohibited_unless:query_data.owners.*.owner_type,Natural'
            ],

            // juristic owner fields
            'query_data.owners.*.enterprise_phone' => [
                'nullable',
                'required_if:query_data.owners.*.owner_type,Juristic',
                'string',
                'prohibited_unless:query_data.owners.*.owner_type,Juristic'
            ],
            'query_data.owners.*.enterprise_email' => [
                'nullable',
                'required_if:query_data.owners.*.owner_type,Juristic',
                'string',
                'email',
                'prohibited_unless:query_data.owners.*.owner_type,Juristic'
            ],
            'query_data.owners.*.enterprise_type' => [
                'nullable',
                'required_if:query_data.owners.*.owner_type,Juristic',
                'string',
                'in:Trust,Enterprise,TRUST,ENTERPRISE',
                'prohibited_unless:query_data.owners.*.owner_type,Juristic'
            ],
            'query_data.owners.*.enterprise_registration_number' => [
                'nullable',
                'required_if:query_data.owners.*.owner_type,Juristic',
                'string',
                'prohibited_unless:query_data.owners.*.owner_type,Juristic'
            ],
            'query_data.owners.*.enterprise_name' => [
                'nullable',
                'required_if:query_data.owners.*.owner_type,Juristic',
                'string',
                'prohibited_unless:query_data.owners.*.owner_type,Juristic'
            ],
            'query_data.owners.*.country_of_registration' => [
                'nullable',
                'required_if:query_data.owners.*.owner_type,Juristic',
                'string',
                'prohibited_unless:query_data.owners.*.owner_type,Juristic'
            ],
            'query_data.owners.*.registration_date' => [
                'nullable',
                'required_if:query_data.owners.*.owner_type,Juristic',
                'date',
                'prohibited_unless:query_data.owners.*.owner_type,Juristic'
            ],

            // shared fields
            'query_data.owners.*.res_address_1' => 'required|string',
            'query_data.owners.*.res_address_2' => 'nullable|string',
            'query_data.owners.*.res_city' => 'required|string',
            'query_data.owners.*.res_province' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $path = str_replace('.res_province', '', $attribute);
                    $country = data_get(request(), $path . '.res_country');
                    if ($country === 'South Africa' && !in_array(strtoupper($value), array_map('strtoupper', array_column(ProvinceSouthAfricaEnum::cases(), 'value')))) {
                        $fail('When country is South Africa, province must be one of: ' . implode(', ', ProvinceSouthAfricaEnum::toArray()));
                    }
                }
            ],
            'query_data.owners.*.res_postal_code' => 'required|string',
            'query_data.owners.*.res_country' => 'required|string',
            'query_data.owners.*.date_received' => 'required|string',
            'query_data.owners.*.ownership' => [
                'required_without:query_data.owners.*.interest_types',
                'numeric',
                'between:5,100'
            ], // TODO: deprecate ownership which have been replaced by interest_types - ETD 2025-01-31 @josh
            'query_data.owners.*.interest_types' => 'required_without:query_data.owners.*.ownership|array|min:1',
            'query_data.owners.*.interest_types.*.name' => ['required_with:query_data.owners.*.interest_types', new Enum(InterestTypeEnum::class)],
            'query_data.owners.*.interest_types.*.value' => [
                'required_if:query_data.owners.*.interest_types.*.name,' . InterestTypeEnum::SHAREHOLDING->value . ',' . InterestTypeEnum::VOTING_RIGHTS->value,
                function ($attribute, $value, $fail) {

                    $path = str_replace('.value', '', $attribute);
                    //$name = data_get($this->all(), $path . '.name'); todo: $this->all() is empty
                    $name = data_get(request(), $path . '.name');
                    if ($name === InterestTypeEnum::SHAREHOLDING->value) {
                        if (!is_numeric($value) || $value < 5 || $value > 100) {
                            $fail('Shareholding value must be between 5 and 100.');
                        }
                    } elseif ($name === InterestTypeEnum::VOTING_RIGHTS->value) {
                        if (!is_numeric($value) || $value > 100) {
                            $fail('Voting rights value must be a number with maximum 100.');
                        }
                    }
                }
            ],

            'query_data.files' => 'required|array',
            'query_data.files.owners' => ['required', 'array',],
            'query_data.files.mandate' => 'required|string',
            'query_data.files.registers' => 'required|array',
            'query_data.files.registers.owners' => 'required|string',
            'query_data.files.registers.shareholders' => 'required_without:query_data.files.registers.members|string',
            'query_data.files.registers.members' => 'required_without:query_data.files.registers.shareholders|string',
            'query_data.company' => 'required|array',
            'query_data.company.registration_number' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    if (str_ends_with($value, "24")) {
                        $fail('Beneficial ownership can not be filed for co-operatives.');
                    }
                }
            ],
            'query_data.company.company_id' => 'required|numeric',
            'query_data.tracking_no' => ['nullable', 'regex:/^\d{11}$/'],
        ]);
    }

    /**
     * Descriptions and examples of the body parameters for the request.
     * NB: Do not provide examples for arrays or objects.
     * e.g. 'reference' => [
     *   'description' => 'The reference number.',
     *   'example' => 'ID123'
     * ]
     */
    public function bodyParameters(): array
    {
        return array_merge($this->getCompanyBodyParameters(), [
            'query_data.owners' => [
                'description' => 'Array of beneficial owners associated with the entity.',
            ],
            'query_data.owners.*.owner_type' => [
                'description' => 'Type of beneficial owner - either "Natural" for individuals or "Juristic" for companies/organizations.',
                'example' => 'Natural'
            ],

            // Natural owner fields
            'query_data.owners.*.first_name' => [
                'description' => 'First name of the natural person.',
                'example' => 'John'
            ],
            'query_data.owners.*.surname' => [
                'description' => 'Surname/last name of the natural person.',
                'example' => 'Smith'
            ],
            'query_data.owners.*.name' => [
                'description' => 'Full name of the natural person.',
                'example' => 'John Smith'
            ],
            'query_data.owners.*.id_number' => [
                'description' => 'South African ID number or passport number.',
                'example' => '8006205069081'
            ],
            'query_data.owners.*.date_of_birth' => [
                'description' => 'Date of birth in YYYY-MM-DD format.',
                'example' => '1980-06-20'
            ],
            'query_data.owners.*.date_issued' => [
                'description' => 'Date when ID or passport was issued in YYYY-MM-DD format.',
                'example' => '2015-06-22'
            ],
            'query_data.owners.*.gender' => [
                'description' => 'Gender. Must be one of the values defined in GenderEnum.',
                'example' => 'Male'
            ],
            'query_data.owners.*.race' => [
                'description' => 'Race. Must be one of the values defined in RaceEnum.',
                'example' => 'White'
            ],
            'query_data.owners.*.citizen' => [
                'description' => 'Whether the person is a South African citizen. Can be boolean or integer (0/1).',
                'example' => true
            ],
            'query_data.owners.*.disability' => [
                'description' => 'Whether the person has a disability. Can be boolean or integer (0/1).',
                'example' => 0
            ],
            'query_data.owners.*.country_of_origin' => [
                'description' => 'Country of origin for the natural person.',
                'example' => 'South Africa'
            ],
            'query_data.owners.*.email' => [
                'description' => 'Email address of the owner.',
                'example' => '<EMAIL>'
            ],
            'query_data.owners.*.phone' => [
                'description' => 'Phone number of the natural person.',
                'example' => '0821234567'
            ],

            // Juristic owner fields
            'query_data.owners.*.enterprise_phone' => [
                'description' => 'Phone number of the juristic entity.',
                'example' => '0214567890'
            ],
            'query_data.owners.*.enterprise_email' => [
                'description' => 'Email address of the juristic entity.',
                'example' => '<EMAIL>'
            ],
            'query_data.owners.*.enterprise_type' => [
                'description' => 'Type of juristic entity. Must be either "Trust" or "Enterprise".',
                'example' => 'Enterprise'
            ],
            'query_data.owners.*.registration_number' => [
                'description' => 'Registration number of the juristic entity.',
                'example' => '2020/123456/07'
            ],
            'query_data.owners.*.enterprise_name' => [
                'description' => 'Name of the juristic entity.',
                'example' => 'ABC Holdings (Pty) Ltd'
            ],
            'query_data.owners.*.country_of_registration' => [
                'description' => 'Country where the juristic entity is registered.',
                'example' => 'South Africa'
            ],
            'query_data.owners.*.registration_date' => [
                'description' => 'Date when the juristic entity was registered in YYYY-MM-DD format.',
                'example' => '2020-01-15'
            ],

            // Shared fields
            'query_data.owners.*.res_address_1' => [
                'description' => 'Residential or business address line 1.',
                'example' => 'Harbour View'
            ],
            'query_data.owners.*.res_address_2' => [
                'description' => 'Residential or business address line 2.',
                'example' => 'Harbour Road'
            ],
            'query_data.owners.*.res_city' => [
                'description' => 'Residential or business city.',
                'example' => 'Port Alfred'
            ],
            'query_data.owners.*.res_province' => [
                'description' => 'Residential or business province. For South African addresses, must be one of the values defined in ProvinceSouthAfricaEnum.',
                'example' => 'Eastern Cape'
            ],
            'query_data.owners.*.res_postal_code' => [
                'description' => 'Residential or business postal code.',
                'example' => '6170'
            ],
            'query_data.owners.*.res_country' => [
                'description' => 'Residential or business country.',
                'example' => 'South Africa'
            ],
            'query_data.owners.*.date_received' => [
                'description' => 'Date when the beneficial interest was received in YYYY-MM-DD format.',
                'example' => '2022-03-15'
            ],
            'query_data.owners.*.ownership' => [
                'description' => 'Ownership percentage (between 5 and 100). Will be deprecated by 2025-01-31.',
                'example' => 25
            ],
            'query_data.owners.*.interest_types' => [
                'description' => 'Array of beneficial interest types.',
            ],
            'query_data.owners.*.interest_types.*.name' => [
                'description' => 'Name of the interest type. Must be one of the values defined in InterestTypeEnum.',
                'example' => 'SHAREHOLDING'
            ],
            'query_data.owners.*.interest_types.*.value' => [
                'description' => 'Value of the interest. For SHAREHOLDING: between 5-100. For VOTING_RIGHTS: number with maximum 100.',
                'example' => 25
            ],

            // File-related fields
            'query_data.files.mandate' => [
                'description' => 'Mandate to file document.',
                'example' => 'mandate.pdf'
            ],
            'query_data.files.registers.owners' => [
                'description' => 'Register of beneficial owners document.',
                'example' => 'owners.pdf'
            ],
            'query_data.files.registers.shareholders' => [
                'description' => 'Register of shareholders document.',
                'example' => 'shareholders.pdf'
            ],
            'query_data.files.registers.members' => [
                'description' => 'Register of members document.',
                'example' => 'members.pdf'
            ],
            'query_data.company' => [
                'description' => 'The company details.',
            ],
            'query_data.company.registration_number' => [
                'description' => 'Company registration number.',
                'example' => '2020/123456/07'
            ],
            'query_data.company.company_id' => [
                'description' => 'Unique identifier for the company.',
                'example' => 12345
            ],
            'query_data.company.has_shareholders' => [
                'description' => 'Indicates if the company has shareholders.',
                'example' => true
            ],
            'query_data.tracking_no' => [
                'description' => 'Tracking number (11 digits).',
                'example' => '12345678901'
            ]
        ]);
    }

    /**
     * Custom validation rules to run after the request is validated.
     * e.g. check if the total ownership is greater than 100%
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $owners = $this->input('query_data.owners', []);
            $totalOwnership = 0;

            foreach ($owners as $index => $owner) {
                if (empty($owner['ownership']) && empty($owner['interest_types'])) {
                    $validator->errors()->add(
                        "query_data.owners.{$index}",
                        'Either ownership or interest types must be provided.'
                    );
                }

                // Calculate total ownership from interest_types if present, otherwise use legacy ownership field
                if (!empty($owner['interest_types'])) {
                    foreach ($owner['interest_types'] as $interest) {
                        if ($interest['name'] === InterestTypeEnum::SHAREHOLDING->value) {
                            $totalOwnership += floatval($interest['value']);
                        }
                    }
                } elseif (isset($owner['ownership'])) {
                    $totalOwnership += floatval($owner['ownership']);
                }
            }

            if ($totalOwnership > 0 && ($totalOwnership > 100.0001)) {
                $validator->errors()->add(
                    'query_data.owners',
                    'Total ownership must not exceed 100%.'
                );
            }

            // TODO: check if the files are valid
        });
    }
}
