<?php

namespace App\Http\Requests;

use App\Enums\AnnualReturnAfsIndustryEnum;

use Illuminate\Validation\Rules\Enum;

class AnnualReturnAfsRequest extends CipcCompanyBaseRequest
{


    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge($this->getCompanyRules(), [
            'query_data.company.phone' => 'required|string',
            'query_data.company.email' => 'required|string|email',
            'query_data.company.website' => 'nullable|string',
            'query_data.company.business_description' => 'nullable|string',
            'query_data.company.industry' => ['required', 'string', new Enum(AnnualReturnAfsIndustryEnum::class)],
            'query_data.periods' => 'required|array|min:1',
            'query_data.periods.*.period' => 'required|string|date_format:Y',
            'query_data.periods.*.turnover' => 'required|numeric',
            'query_data.files' => 'required|array',
            'query_data.files.xbrl' => 'required|string',
            'query_data.files.pdf' => 'nullable|string',
        ]);
    }

    /**
     * Get the body parameter descriptions for the request.
     */
    public function bodyParameters(): array
    {
        return array_merge($this->getCompanyBodyParameters(), [
            'query_data.type' => [
                'description' => 'The type of annual return submission.',
                'example' => 'afs'
            ],
            'query_data.company.phone' => [
                'description' => 'Company phone number.',
                'example' => '0821234567'
            ],
            'query_data.company.email' => [
                'description' => 'Company email address.',
                'example' => '<EMAIL>'
            ],
            'query_data.company.website' => [
                'description' => 'Company website URL.',
                'example' => 'https://example.com'
            ],
            'query_data.company.business_description' => [
                'description' => 'Brief description of company business.',
                'example' => 'Software development services'
            ],
            'query_data.company.industry' => [
                'description' => 'Company standard industry code (SIC).',
                'example' => AnnualReturnAfsIndustryEnum::CONSTRUCTION->value
            ],
            'query_data.periods' => [
                'description' => 'The financial periods for submission.',
            ],
            'query_data.periods.*.period' => [
                'description' => 'The year of the financial period.',
                'example' => '2025'
            ],
            'query_data.periods.*.turnover' => [
                'description' => 'Company turnover for the period in Rands.',
                'example' => 1000000
            ],
            'query_data.files' => [
                'description' => 'The required financial statement files.',
            ],
            'query_data.files.xbrl' => [
                'description' => 'The XBRL file name for the financial statements.',
                'example' => 'financial_statements.xbrl'
            ],
            'query_data.files.pdf' => [
                'description' => 'Optional PDF version of the financial statements.',
                'example' => 'financial_statements.pdf'
            ],
        ]);
    }

    /**
     * Custom validation rules to run after the request is validated.
     */
    public function withValidator($validator): void
    {
        // TODO: check if the files are valid
    }
}
