<?php

namespace App\Http\Requests;


use Illuminate\Foundation\Http\FormRequest;
use App\Http\Requests\CipcBaseRequest;

class CompanyRegistrationRequest extends CipcCompanyBaseRequest
{


    public function authorize()
    {
        return true; // Adjust as necessary
    }

    public function rules(): array
    {
        return [
            'reference' => 'required',
            'customer_code' => 'required|string',
            'customer_password' => 'required|string',
            'query_data' => 'required|array',
            'query_data.company.financial_year_end' => 'required|string',
            'query_data.company.authorised_shares' => 'required|numeric',
            'query_data.company.email' => 'required|email',
            'query_data.company.website' => 'nullable|string',
            'query_data.company.physical_address_1' => 'required|string',
            'query_data.company.physical_address_2' => 'nullable|string',
            'query_data.company.physical_city' => 'required|string',
            'query_data.company.physical_province' => 'required|string',
            'query_data.company.physical_postal_code' => 'required|string',
            'query_data.company.pos_address_1' => 'required|string',
            'query_data.company.pos_address_2' => 'nullable|string',
            'query_data.company.pos_city' => 'required|string',
            'query_data.company.pos_province' => 'required|string',
            'query_data.company.pos_postal_code' => 'required|string',
            'query_data.directors.*.first_name' => 'required|string',
            'query_data.directors.*.second_name' => 'nullable|string',
            'query_data.directors.*.surname' => 'required|string',
            'query_data.directors.*.citizen' => 'required|boolean',
            'query_data.directors.*.id_number' => 'required|string',
            'query_data.directors.*.date_of_birth' => 'required_if:query_data.directors.*.citizen,false|nullable|date',
            'query_data.directors.*.country_of_origin' => 'required_if:query_data.directors.*.citizen,false|nullable|string',
            'query_data.directors.*.role' => 'required|string',
            'query_data.directors.*.phone' => 'required|string',
            'query_data.directors.*.email' => 'required|email',
            'query_data.directors.*.res_address_1' => 'required|string',
            'query_data.directors.*.res_address_2' => 'nullable|string',
            'query_data.directors.*.res_city' => 'required|string',
            'query_data.directors.*.res_province' => 'required|string',
            'query_data.directors.*.res_postal_code' => 'required|string',
            'query_data.directors.*.pos_address_1' => 'required|string',
            'query_data.directors.*.pos_address_2' => 'nullable|string',
            'query_data.directors.*.pos_city' => 'required|string',
            'query_data.directors.*.pos_province' => 'required|string',
            'query_data.directors.*.pos_postal_code' => 'required|string',
            'query_data.name_reservation.name_1' => 'required|string',
            'query_data.name_reservation.name_2' => 'nullable|string',
            'query_data.name_reservation.name_3' => 'nullable|string',
            'query_data.name_reservation.name_4' => 'nullable|string',
        ];
    }

    public function bodyParameters(): array
    {
        return [
            'reference' => [
                'description' => 'The transaction ID for this request',
                'example' => '12345',
                'required' => true,
                'type' => 'integer'
            ],
            'customer_code' => [
                'description' => 'The customer code for authentication',
                'example' => 'CUST001',
                'required' => true,
                'type' => 'string'
            ],
            'customer_password' => [
                'description' => 'The customer password for authentication',
                'example' => 'password123',
                'required' => true,
                'type' => 'string'
            ],
            'query_data' => [
                'description' => 'The query data containing company and director information',
                'required' => true,
                'type' => 'object'
            ],
            'query_data.company' => [
                'description' => 'The company details.',
                'required' => true,
                'type' => 'object'
            ],
            'query_data.company.financial_year_end' => [
                'description' => 'The month of financial year end',
                'example' => 'February',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.company.authorised_shares' => [
                'description' => 'Number of authorised shares',
                'example' => 100,
                'required' => true,
                'type' => 'integer'
            ],
            'query_data.company.email' => [
                'description' => 'Company email address',
                'example' => '<EMAIL>',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.company.website' => [
                'description' => 'Company website',
                'example' => null,
                'required' => false,
                'type' => 'string'
            ],
            'query_data.company.physical_address_1' => [
                'description' => 'Physical address line 1',
                'example' => '26 Roseberry Avenue',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.company.physical_address_2' => [
                'description' => 'Physical address line 2',
                'example' => 'Oranjezicht',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.company.physical_city' => [
                'description' => 'Physical address city',
                'example' => 'Cape Town',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.company.physical_province' => [
                'description' => 'Physical address province',
                'example' => 'Western Cape',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.company.physical_postal_code' => [
                'description' => 'Physical address postal code',
                'example' => '8001',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.company.pos_address_1' => [
                'description' => 'Postal address line 1',
                'example' => '26 Roseberry Avenue',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.company.pos_address_2' => [
                'description' => 'Postal address line 2',
                'example' => 'Oranjezicht',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.company.pos_city' => [
                'description' => 'Postal address city',
                'example' => 'Cape Town',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.company.pos_province' => [
                'description' => 'Postal address province',
                'example' => 'Western Cape',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.company.pos_postal_code' => [
                'description' => 'Postal address postal code',
                'example' => '8001',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors' => [
                'description' => 'The directors data.',
                'required' => true,
                'type' => 'array'
            ],
            'query_data.directors.*.first_name' => [
                'description' => 'Director first name',
                'example' => 'John',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.second_name' => [
                'description' => 'Director second name',
                'example' => 'James',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.directors.*.surname' => [
                'description' => 'Director surname',
                'example' => 'Smith',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.citizen' => [
                'description' => 'Whether the director is a citizen',
                'example' => true,
                'required' => true,
                'type' => 'boolean'
            ],
            'query_data.directors.*.id_number' => [
                'description' => 'Director ID number',
                'example' => '8001011234081',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.date_of_birth' => [
                'description' => 'Director date of birth',
                'example' => '1980-01-01',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.directors.*.country_of_origin' => [
                'description' => 'Director country of origin',
                'example' => 'South Africa',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.directors.*.role' => [
                'description' => 'Director role',
                'example' => 'Director',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.phone' => [
                'description' => 'Director phone number',
                'example' => '0112345678',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.email' => [
                'description' => 'Director email address',
                'example' => '<EMAIL>',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.res_address_1' => [
                'description' => 'Director residential address line 1',
                'example' => '123 Main St',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.res_address_2' => [
                'description' => 'Director residential address line 2',
                'example' => 'Sandton',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.directors.*.res_city' => [
                'description' => 'Director residential address city',
                'example' => 'Johannesburg',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.res_province' => [
                'description' => 'Director residential address province',
                'example' => 'Gauteng',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.res_postal_code' => [
                'description' => 'Director residential address postal code',
                'example' => '2191',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.pos_address_1' => [
                'description' => 'Director postal address line 1',
                'example' => '123 Main St',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.pos_address_2' => [
                'description' => 'Director postal address line 2',
                'example' => 'Sandton',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.directors.*.pos_city' => [
                'description' => 'Director postal address city',
                'example' => 'Johannesburg',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.pos_province' => [
                'description' => 'Director postal address province',
                'example' => 'Gauteng',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.pos_postal_code' => [
                'description' => 'Director postal address postal code',
                'example' => '2191',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.name_reservation' => [
                'description' => 'The name reservation data.',
                'required' => true,
                'type' => 'object'
            ],
            'query_data.name_reservation.name_1' => [
                'description' => 'First company name option',
                'example' => 'Company Name 1',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.name_reservation.name_2' => [
                'description' => 'Second company name option',
                'example' => 'Company Name 2',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.name_reservation.name_3' => [
                'description' => 'Third company name option',
                'example' => 'Company Name 3',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.name_reservation.name_4' => [
                'description' => 'Fourth company name option',
                'example' => 'Company Name 4',
                'required' => false,
                'type' => 'string'
            ]
        ];
    }



    public function messages(): array
    {
        return [
            // Authentication fields
            'reference.required' => 'The transaction ID is required.',
            'customer_code.required' => 'The customer code is required.',
            'customer_password.required' => 'The customer password is required.',

            // Company fields
            'query_data.company.financial_year_end.required' => 'The financial year end is required.',
            'query_data.company.authorised_shares.required' => 'The authorised shares field is required.',
            'query_data.company.authorised_shares.numeric' => 'The authorised shares must be a number.',
            'query_data.company.email.required' => 'The company email is required.',
            'query_data.company.email.email' => 'Please provide a valid company email address.',
            'query_data.company.website.string' => 'The company website must be a valid URL.',

            // Company physical address
            'query_data.company.physical_address_1.required' => 'The company physical address line 1 is required.',
            'query_data.company.physical_address_2.string' => 'The company physical address line 2 must be text.',
            'query_data.company.physical_city.required' => 'The company physical address city is required.',
            'query_data.company.physical_province.required' => 'The company physical address province is required.',
            'query_data.company.physical_postal_code.required' => 'The company physical address postal code is required.',

            // Company postal address
            'query_data.company.pos_address_1.required' => 'The company postal address line 1 is required.',
            'query_data.company.pos_address_2.string' => 'The company postal address line 2 must be text.',
            'query_data.company.pos_city.required' => 'The company postal address city is required.',
            'query_data.company.pos_province.required' => 'The company postal address province is required.',
            'query_data.company.pos_postal_code.required' => 'The company postal address postal code is required.',

            // Director fields
            'query_data.directors.*.first_name.required' => 'The director first name is required.',
            'query_data.directors.*.first_name.string' => 'The director first name must be text.',
            'query_data.directors.*.second_name.string' => 'The director second name must be text.',
            'query_data.directors.*.surname.required' => 'The director surname is required.',
            'query_data.directors.*.surname.string' => 'The director surname must be text.',
            'query_data.directors.*.citizen.required' => 'Please specify whether the director is a citizen.',
            'query_data.directors.*.citizen.boolean' => 'The citizen field must be true or false.',
            'query_data.directors.*.id_number.required' => 'The director ID number is required.',
            'query_data.directors.*.id_number.string' => 'The director ID number must be text.',
            'query_data.directors.*.date_of_birth.required_if' => 'The date of birth is required for non-citizen directors.',
            'query_data.directors.*.date_of_birth.date' => 'The director date of birth must be a valid date.',
            'query_data.directors.*.country_of_origin.required_if' => 'The country of origin is required for non-citizen directors.',
            'query_data.directors.*.country_of_origin.string' => 'The director country of origin must be text.',
            'query_data.directors.*.role.required' => 'The director role is required.',
            'query_data.directors.*.role.string' => 'The director role must be text.',
            'query_data.directors.*.phone.required' => 'The director phone number is required.',
            'query_data.directors.*.phone.string' => 'The director phone number must be text.',
            'query_data.directors.*.email.required' => 'The director email is required.',
            'query_data.directors.*.email.email' => 'Please provide a valid director email address.',

            // Director residential address
            'query_data.directors.*.res_address_1.required' => 'The director residential address line 1 is required.',
            'query_data.directors.*.res_address_1.string' => 'The director residential address line 1 must be text.',
            'query_data.directors.*.res_address_2.string' => 'The director residential address line 2 must be text.',
            'query_data.directors.*.res_city.required' => 'The director residential address city is required.',
            'query_data.directors.*.res_city.string' => 'The director residential address city must be text.',
            'query_data.directors.*.res_province.required' => 'The director residential address province is required.',
            'query_data.directors.*.res_province.string' => 'The director residential address province must be text.',
            'query_data.directors.*.res_postal_code.required' => 'The director residential address postal code is required.',
            'query_data.directors.*.res_postal_code.string' => 'The director residential address postal code must be text.',

            // Director postal address
            'query_data.directors.*.pos_address_1.required' => 'The director postal address line 1 is required.',
            'query_data.directors.*.pos_address_1.string' => 'The director postal address line 1 must be text.',
            'query_data.directors.*.pos_address_2.string' => 'The director postal address line 2 must be text.',
            'query_data.directors.*.pos_city.required' => 'The director postal address city is required.',
            'query_data.directors.*.pos_city.string' => 'The director postal address city must be text.',
            'query_data.directors.*.pos_province.required' => 'The director postal address province is required.',
            'query_data.directors.*.pos_province.string' => 'The director postal address province must be text.',
            'query_data.directors.*.pos_postal_code.required' => 'The director postal address postal code is required.',
            'query_data.directors.*.pos_postal_code.string' => 'The director postal address postal code must be text.',

            // Name reservation fields
            'query_data.name_reservation.name_1.required' => 'At least one company name option is required.',
            'query_data.name_reservation.name_1.string' => 'The first company name option must be text.',
            'query_data.name_reservation.name_2.string' => 'The second company name option must be text.',
            'query_data.name_reservation.name_3.string' => 'The third company name option must be text.',
            'query_data.name_reservation.name_4.string' => 'The fourth company name option must be text.',
        ];
    }
}
