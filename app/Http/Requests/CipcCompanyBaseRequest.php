<?php

namespace App\Http\Requests;

class CipcCompanyBaseRequest extends CipcBaseRequest
{
    /**
     * Get the validation rules that apply to all CIPC company requests.
     */
    protected function getCompanyRules(): array
    {
        return array_merge($this->getCipcRules(), [
            'query_data.company' => 'required|array',
            'query_data.company.registration_number' => 'required|string|regex:/^[0-9]{4}\/[0-9]{6}\/[0-9]{2}$/',
        ]);
    }

    /**
     * Get the body parameter descriptions that apply to all CIPC company requests.
     */
    protected function getCompanyBodyParameters(): array
    {
        return array_merge($this->getCipcBodyParameters(), [
            'query_data.company' => [
                'description' => 'The company details.',
            ],
            'query_data.company.registration_number' => [
                'description' => 'Company registration number.',
                'example' => '2020/123456/07'
            ],
        ]);
    }
}
