<?php

namespace App\Http\Requests;


use Illuminate\Foundation\Http\FormRequest;

class SubmissionBaseRequest extends FormRequest
{


    /**
     * Get the base validation rules that apply to all submissions.
     */
    protected function getBaseRules(): array
    {
        return [
            'reference' => 'required',
            'query_data' => 'required|array',
        ];
    }

    /**
     * Get the base body parameter descriptions that apply to all submissions.
     */
    protected function getBaseBodyParameters(): array
    {
        return [
            'reference' => [
                'description' => 'The reference number.',
                'example' => 'ID123'
            ],
            'query_data' => [
                'description' => 'The submission data.',
            ],
        ];
    }
}
