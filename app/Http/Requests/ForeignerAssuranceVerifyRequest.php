<?php

namespace App\Http\Requests;


use Illuminate\Foundation\Http\FormRequest;

class ForeignerAssuranceVerifyRequest extends FormRequest
{


    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'passportNumbers' => ['required', 'string', 'regex:/^[A-Z0-9]+(,[A-Z0-9]+){0,2}$/'],
        ];
    }

    public function bodyParameters(): array
    {
        return [
            'passportNumbers' => [
                'description' => 'A comma separated list of passport numbers (3 max).',
                'example' => '123456,789012,345678'
            ]
        ];
    }
}
