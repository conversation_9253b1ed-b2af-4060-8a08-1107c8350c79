<?php

namespace App\Http\Requests;


use DateTime;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DirectorChangeRequest extends CipcCompanyBaseRequest
{


    public function authorize()
    {
        return true; // Adjust as necessary
    }

    public function rules()
    {
        // Check if this is a verify request by looking for OTPs or tracking number
        if ($this->isVerifyRequest()) {
            return $this->getVerifyRules();
        }

        return array_merge($this->getCompanyRules(), [

            'query_data' => [
                'required',
                'array',
                function ($attribute, $value, $fail) {
                    $this->validateDirectorChangeResignationDate($value, $fail);
                },
                function ($attribute, $value, $fail) {
                    $this->validateDirectorChangeMinDirectors($value, $fail);
                },
                function ($attribute, $value, $fail) {
                    $this->validateDirectorChangeAppointmentDate($value, $fail);
                },
                function ($attribute, $value, $fail) {
                    $this->validateDirectorChangeDuplicateDirectors($value, $fail);
                },
            ],
            // Top-level required fields
            'query_data.company' => 'required',
            'query_data.company.company_name' => 'required|string',
            'query_data.company.phone' => 'required|string|regex:/^[0-9]{10}$/',
            'query_data.company.email' => 'required|string|email',

            // Directors array
            'query_data.directors.*.appointment_date' => 'required|string',
            'query_data.directors.*.individual' => 'required',
            'query_data.directors.*.individual.first_name' => 'required|string',
            'query_data.directors.*.individual.second_name' => 'nullable|string',
            'query_data.directors.*.individual.surname' => 'required|string',
            'query_data.directors.*.individual.id_number' => 'required|string',
            'query_data.directors.*.individual.disability' => 'required|boolean',
            'query_data.directors.*.individual.gender' => [
                'required',
                'string',
                Rule::in(['Male', 'Female', 'Other']),
            ],
            'query_data.directors.*.individual.race' => [
                'required',
                'string',
                Rule::in(['African', 'Asian', 'Coloured', 'Indian', 'White']),
            ],
            'query_data.directors.*.individual.title' => [
                'required',
                'string',
                Rule::in(['Mr', 'Mrs', 'Ms', 'Dr', 'Adv']),
            ],

            'query_data.directors.*.email' => 'required|string|email',
            'query_data.directors.*.phone' => 'required|string',
            'query_data.directors.*.status.name' => [
                'required',
                'string',
                Rule::in(['Active', 'Pending Resignation', 'Pending Removal', 'Pending Deceasement']),
            ],

            // Conditional field: resignation_date
            'query_data.directors.*.resignation_date' => [
                'nullable',
                'date',
                function ($attribute, $value, $fail) {
                    $statusName = data_get(request(), str_replace('resignation_date', 'status.name', $attribute));
                    if (in_array($statusName, ['Pending Resignation', 'Pending Removal', 'Pending Deceasement']) && is_null($value)) {
                        $fail("The {$attribute} field is required when status is '{$statusName}'.");
                    }
                },
            ],

            'query_data.directors.*.occupation' => 'nullable|string',
            // New Directors array (optional)
            'query_data.new_directors' => 'sometimes',
            'query_data.new_directors.*.individual' => 'required',
            'query_data.new_directors.*.individual.first_name' => 'required|string',
            'query_data.new_directors.*.individual.second_name' => 'nullable|string',
            'query_data.new_directors.*.individual.surname' => 'required|string',
            'query_data.new_directors.*.individual.id_number' => 'required|string',
            'query_data.new_directors.*.individual.citizen' => 'required|boolean',
            'query_data.new_directors.*.individual.gender' => [
                'required',
                'string',
                Rule::in(['Male', 'Female', 'Other']),
            ],
            'query_data.new_directors.*.individual.race' => [
                'required',
                'string',
                Rule::in(['African', 'Asian', 'Coloured', 'Indian', 'White']),
            ],
            'query_data.new_directors.*.individual.title' => [
                'required',
                'string',
                Rule::in(['Mr', 'Mrs', 'Ms', 'Dr', 'Adv']),
            ],
            'query_data.new_directors.*.individual.disability' => 'required|boolean',

            // Conditional fields based on 'citizen' status
            'query_data.new_directors.*.individual.date_issued' => [
                'nullable',
                'date',
                'required_if:new_directors.*.individual.citizen,1',
            ],
            'query_data.new_directors.*.individual.date_expired' => [
                'nullable',
                'date',
                'required_if:new_directors.*.individual.citizen,0',
            ],

            'query_data.new_directors.*.status' => 'required',
            'query_data.new_directors.*.status.name' => [
                'required',
                'string',
            ],

            'query_data.new_directors.*.email' => 'required|string|email',
            'query_data.new_directors.*.phone' => 'required|string',

            'query_data.new_directors.*.role' => [
                'required',
                'string',
                Rule::in(['DIRECTOR', 'NON EXECUTIVE DIRECTOR', 'ALTERNATING DIRECTOR']),
            ],
            'query_data.new_directors.*.appointment_date' => 'required|date',
            'query_data.new_directors.*.occupation' => 'nullable|string',

            // Docs field: either a string or an array of strings
            'query_data.docs' => [
                'nullable',
                function ($attribute, $value, $fail) {
                    if (!is_string($value) && !is_array($value)) {
                        $fail("The {$attribute} must be a string or an array of strings.");
                    }

                    if (is_array($value)) {
                        foreach ($value as $doc) {
                            if (!is_string($doc)) {
                                $fail("Each item in {$attribute} must be a string.");
                                break;
                            }
                        }
                    }
                },
            ],
        ]);
    }

    /**
     * Descriptions and examples of the body parameters for the request.
     * NB: Do not provide examples for arrays or objects.
     * e.g. 'reference' => [
     *   'description' => 'The reference number.',
     *   'example' => 'ID123'
     * ]
     */
    public function bodyParameters(): array
    {
        // Check if this is a verify request
        if ($this->isVerifyRequest()) {
            return $this->getVerifyBodyParameters();
        }

        return array_merge($this->getCompanyBodyParameters(), [
            'query_data.company.company_name' => [
                'description' => 'The registered company name.',
                'example' => 'Example Company (Pty) Ltd'
            ],
            'query_data.company.phone' => [
                'description' => 'Company phone number (10 digits).',
                'example' => '0123456789'
            ],
            'query_data.company.email' => [
                'description' => 'Company email address.',
                'example' => '<EMAIL>'
            ],
            'query_data.directors' => [
                'description' => 'Array of current directors.',
            ],
            'query_data.directors.*.appointment_date' => [
                'description' => 'Director appointment date.',
                'example' => '2024-03-20'
            ],
            'query_data.directors.*.individual.first_name' => [
                'example' => 'John'
            ],
            'query_data.directors.*.individual.second_name' => [
                'description' => 'Optional second name.',
                'example' => 'William'
            ],
            'query_data.directors.*.individual.surname' => [
                'example' => 'Smith'
            ],
            'query_data.directors.*.individual.id_number' => [
                'description' => 'ID number.',
                'example' => '8001015009087'
            ],
            'query_data.directors.*.individual.disability' => [
                'description' => 'Disability status.',
                'example' => false
            ],
            'query_data.directors.*.individual.gender' => [
                'description' => 'Gender (Male, Female, Other).',
                'example' => 'Male'
            ],
            'query_data.directors.*.individual.race' => [
                'description' => 'Race classification.',
                'example' => 'African'
            ],
            'query_data.directors.*.individual.title' => [
                'description' => 'Title (Mr, Mrs, Ms, Dr, Adv).',
                'example' => 'Mr'
            ],
            'query_data.directors.*.email' => [
                'example' => '<EMAIL>'
            ],
            'query_data.directors.*.phone' => [
                'example' => '0723456789'
            ],
            'query_data.directors.*.status.name' => [
                'description' => 'Current director status.',
                'example' => 'Active'
            ],
            'query_data.directors.*.resignation_date' => [
                'description' => 'Required when status is "Pending Resignation".',
                'example' => '2024-03-20'
            ],
            'query_data.directors.*.occupation' => [
                'description' => 'Optional occupation.',
                'example' => 'Business Executive'
            ],
            'query_data.new_directors' => [
                'description' => 'Array of new directors to be appointed.',
            ],
            'query_data.new_directors.*.individual.first_name' => [
                'example' => 'Jane'
            ],
            'query_data.new_directors.*.individual.second_name' => [
                'description' => 'Optional second name.',
                'example' => 'Elizabeth'
            ],
            'query_data.new_directors.*.individual.surname' => [
                'example' => 'Doe'
            ],
            'query_data.new_directors.*.individual.id_number' => [
                'description' => 'ID number.',
                'example' => '7501015009087'
            ],
            'query_data.new_directors.*.individual.citizen' => [
                'description' => 'SA citizenship status.',
                'example' => true
            ],
            'query_data.new_directors.*.individual.disability' => [
                'description' => 'Disability status.',
                'example' => false
            ],
            'query_data.new_directors.*.individual.gender' => [
                'description' => 'Gender (Male, Female, Other).',
                'example' => 'Female'
            ],
            'query_data.new_directors.*.individual.race' => [
                'description' => 'Race classification.',
                'example' => 'Indian'
            ],
            'query_data.new_directors.*.individual.title' => [
                'description' => 'Title (Mr, Mrs, Ms, Dr, Adv).',
                'example' => 'Mrs'
            ],
            'query_data.new_directors.*.individual.date_issued' => [
                'description' => 'ID issue date (required for citizens).',
                'example' => '2010-01-01'
            ],
            'query_data.new_directors.*.individual.date_expired' => [
                'description' => 'Permit/passport expiry (required for non-citizens).',
                'example' => '2025-12-31'
            ],
            'query_data.new_directors.*.email' => [
                'example' => '<EMAIL>'
            ],
            'query_data.new_directors.*.phone' => [
                'example' => '0834567890'
            ],
            'query_data.new_directors.*.role' => [
                'description' => 'Director role type.',
                'example' => 'DIRECTOR'
            ],
            'query_data.new_directors.*.appointment_date' => [
                'description' => 'Planned appointment date.',
                'example' => '2024-04-01'
            ],
            'query_data.new_directors.*.occupation' => [
                'description' => 'Optional occupation.',
                'example' => 'Financial Analyst'
            ],
            'query_data.docs' => [
                'description' => 'Supporting documents (string or array of strings).',
                'example' => 'document1.pdf'
            ]
        ]);
    }

    protected function validateDirectorChangeResignationDate($value, $fail): void
    {

        $directors = $value['directors'];

        foreach ($directors as $director) {
            if (isset($director['status']) && isset($director['status']['name'])) {
                $statusName = strtolower($director['status']['name']);
                if (preg_match('/resign|remove|deceased/', $statusName)) {
                    if (isset($director['resignation_date'])) {
                        if (isset($director['appointment_date'])) {
                            $appointmentDate = DateTime::createFromFormat('Y-m-d', $director['appointment_date']);
                            $resignationDate = DateTime::createFromFormat('Y-m-d', $director['resignation_date']);

                            if ($resignationDate && $appointmentDate && $resignationDate <= $appointmentDate) {
                                $fail("Resignation date should be after appointment date for {$director['individual']['first_name']}");
                            }
                        } else {
                            $fail("Missing appointment date for {$director['individual']['first_name']}");
                        }
                    } else {
                        $fail("Missing resignation date for {$director['individual']['first_name']}");
                    }
                }
            } else {
                $fail("Missing status for {$director['individual']['first_name']}");
            }
        }
    }

    protected function validateDirectorChangeMinDirectors($value, $fail): void
    {
        $directors = $value['directors'];
        $newDirectors = $value['new_directors'];
        $activeDirectors = [];
        $registrationNumber = request('query_data.company.registration_number');

        if (str_ends_with($registrationNumber, "08")) {
            foreach ($directors as $director) {
                if (!isset($director['resignation_date']) || (isset($director['status']['name']) && $director['status']['name'] !== "Pending Resignation")) {
                    $activeDirectors[] = $director['individual']['id_number'];
                }
            }
            foreach ($newDirectors as $director) {
                if (isset($director['status']['name']) && $director['status']['name'] === "Pending Appointment") {
                    $activeDirectors[] = $director['individual']['id_number'];
                }
            }
            if (count($activeDirectors) < 3) {
                $fail("Minimum of 3 directors required for NPC");
            }
        } elseif (str_ends_with($registrationNumber, "07")) {
            foreach ($directors as $director) {
                if (!isset($director['resignation_date']) || (isset($director['status']['name']) && $director['status']['name'] !== "Pending Resignation")) {
                    $activeDirectors[] = $director['individual']['id_number'];
                }
            }
            foreach ($newDirectors as $director) {
                if (isset($director['status']['name']) && $director['status']['name'] === "Pending Appointment") {
                    $activeDirectors[] = $director['individual']['id_number'];
                }
            }
            if (count($activeDirectors) < 1) {
                $fail("Minimum of 1 director required for PTY");
            }
        }
    }

    protected function validateDirectorChangeAppointmentDate($value, $fail): void
    {
        $directors = $value['directors'];
        $newDirectors = $value['new_directors'] ?? [];
        $registrationNumber = $value['company']['registration_number'] ?? null;
        $registrationYear = substr($registrationNumber, 0, 4);

        foreach ($directors as $director) {
            if (isset($director['appointment_date'])) {
                $appointmentYear = substr($director['appointment_date'], 0, 4);
                if ($appointmentYear < $registrationYear) {
                    $fail("Appointment date should be after company registration date for {$director['individual']['first_name']}");
                }
            }
        }
        foreach ($newDirectors as $director) {
            if (isset($director['appointment_date'])) {
                $appointmentYear = substr($director['appointment_date'], 0, 4);
                if ($appointmentYear < $registrationYear) {
                    $fail("Appointment date should be after company registration date for {$director['individual']['first_name']}");
                }
            }
        }
    }

    protected function validateDirectorChangeDuplicateDirectors($value, $fail): void
    {
        $directorIds = [];
        $directors = $value['directors'];
        $newDirectors = $value['new_directors'];
        foreach ($directors as $director) {
            $directorIds[] = $director['individual']['id_number'];
        }

        foreach ($newDirectors as $director) {
            $directorIds[] = $director['individual']['id_number'];
        }

        $counts = array_count_values($directorIds);
        $duplicates = array_filter($counts, function ($count) {
            return $count > 1;
        });

        if (!empty($duplicates)) {
            $fail("Duplicate directors found");
        }
    }

    /**
     * Check if this is a verify request by looking for OTPs or tracking number
     */
    protected function isVerifyRequest(): bool
    {
        $data = $this->all();

        // Check for verify stage data structure
        if (isset($data['cipc_transaction']) && isset($data['cipc_transaction']['tracking_no'])) {
            return true;
        } elseif (isset($data['directors']) && isset($data['directors'][0]['otps'])) {
            return true;
        }
        return false;
    }

    /**
     * Get validation rules for verify stage
     */
    protected function getVerifyRules(): array
    {
        return  [
            'cipc_transaction' => 'required|array',
            'cipc_transaction.tracking_no' => 'required|string',

            'directors' => 'required|array',
            'directors.*.full_name' => 'required|string',
            'directors.*.id_number' => 'required|string',
            'directors.*.otps' => 'required|array',
            'directors.*.otps.*' => 'required|string',
        ];
    }

    /**
     * Get body parameters for verify stage
     */
    protected function getVerifyBodyParameters(): array
    {
        return array_merge($this->getCipcBodyParameters(), [
            'cipc_transaction' => [
                'description' => 'CIPC transaction details.',
            ],
            'cipc_transaction.id' => [
                'description' => 'The CIPC transaction ID.',
                'example' => 251364
            ],
            'cipc_transaction.tracking_no' => [
                'description' => 'The CIPC tracking number.',
                'example' => '60007404369'
            ],
            'directors' => [
                'description' => 'Array of directors requiring OTP verification.',
            ],
            'directors.*.full_name' => [
                'description' => 'Full name of the director.',
                'example' => 'JOSHUA PHILIP ALEXANDRE'
            ],
            'directors.*.id_number' => [
                'description' => 'ID number of the director.',
                'example' => '9511226118088'
            ],
            'directors.*.otps' => [
                'description' => 'Array of OTP codes for the director.',
            ],
            'directors.*.otps.*' => [
                'description' => 'Individual OTP code.',
                'example' => 'E8393873'
            ],
        ]);
    }
}
