<?php

namespace App\Http\Requests;

class MemberChangeRequest extends CipcCompanyBaseRequest
{
    public function rules()
    {
        return array_merge($this->getCipcRules(), [
            'query_data' => [
                'required',
                'array',
                function ($attribute, $value, $fail) {
                    $this->memberChangeSizeValidator($value, $fail);
                },
                function ($attribute, $value, $fail) {
                    $this->memberChangeDuplicateValidator($value, $fail);
                },
            ],
            'query_data.members.*.id_number' => 'required|string',
            'query_data.members.*.first_name' => 'required|string',
            'query_data.members.*.second_name' => 'nullable|string',
            'query_data.members.*.last_name' => 'required|string',
            'query_data.members.*.status' => ['required', 'string', 'in:Active,Deceased,Resigned,Disqualified,Remove,Retired'],
            'query_data.members.*.resignation_date' => 'nullable|date_format:Y-m-d',
            'query_data.members.*.citizen' => 'required|boolean',
            'query_data.members.*.country_of_origin' => 'required|string',
            'query_data.members.*.phone' => 'required|string',
            'query_data.members.*.email' => 'required|email',
            'query_data.members.*.date_of_birth' => 'nullable|date_format:Y-m-d',
            'query_data.members.*.res_address_1' => 'required|string',
            'query_data.members.*.res_address_2' => 'nullable|string',
            'query_data.members.*.res_city' => 'required|string',
            'query_data.members.*.res_province' => 'required|string',
            'query_data.members.*.res_postal_code' => 'required|string',
            'query_data.members.*.pos_address_1' => 'required|string',
            'query_data.members.*.pos_address_2' => 'nullable|string',
            'query_data.members.*.pos_city' => 'required|string',
            'query_data.members.*.pos_province' => 'required|string',
            'query_data.members.*.pos_postal_code' => 'required|string',
            'query_data.members.*.member_size' => 'required|numeric',
            'query_data.members.*.member_contribution' => 'required|numeric',
            'query_data.new_members' => 'present|array',
            'query_data.new_members.*.id_number' => 'required|string',
            'query_data.new_members.*.first_name' => 'required|string',
            'query_data.new_members.*.second_name' => 'nullable|string',
            'query_data.new_members.*.last_name' => 'required|string',
            'query_data.new_members.*.citizen' => 'required|boolean',
            'query_data.new_members.*.country_of_origin' => 'required|string',
            'query_data.new_members.*.appointment_date' => [
                'required',
                'date_format:Y-m-d',
                function ($attribute, $value, $fail) {
                    $date = \DateTime::createFromFormat('Y-m-d', $value);
                    if (!$date) {
                        $fail('The appointment date must be a valid date in YYYY-MM-DD format.');
                        return;
                    }

                    // Check if the date is in the past
                    $today = new \DateTime();
                    if ($date < $today) {
                        //$fail('You cannot backdate the appointment date.');
                    }
                }
            ],
            'query_data.new_members.*.phone' => 'required|string',
            'query_data.new_members.*.email' => 'required|email',
            'query_data.new_members.*.date_of_birth' => 'required|date_format:Y-m-d',
            'query_data.new_members.*.res_address_1' => 'required|string',
            'query_data.new_members.*.res_address_2' => 'nullable|string',
            'query_data.new_members.*.res_city' => 'required|string',
            'query_data.new_members.*.res_province' => 'required|string',
            'query_data.new_members.*.res_postal_code' => 'required|string',
            'query_data.new_members.*.pos_address_1' => 'required|string',
            'query_data.new_members.*.pos_address_2' => 'nullable|string',
            'query_data.new_members.*.pos_city' => 'required|string',
            'query_data.new_members.*.pos_province' => 'required|string',
            'query_data.new_members.*.pos_postal_code' => 'required|string',
            'query_data.new_members.*.member_size' => 'required|integer',
            'query_data.new_members.*.member_contribution' => 'required|numeric',
        ]);
    }

    public function memberChangeSizeValidator($value, $fail): void
    {
        $members    = $value['members'] ?? [];
        $newMembers = $value['new_members'] ?? [];

        // Keep only active members (no resignation_date)
        $activeMembers = array_filter($members, fn($m) => empty($m['resignation_date']));

        // Combine
        $allMembers = array_merge($newMembers, $activeMembers);

        $memberSize = 0.0;

        foreach ($allMembers as $i => $member) {
            $raw = $member['member_size'] ?? null;

            if (!is_numeric($raw)) {
                $fail("Member #" . ($i + 1) . " member_size must be numeric.");
                continue;
            }

            $size = (float) $raw;

            if ($size <= 0) {
                $fail("Each member's size must be greater than 0.");
            }

            $memberSize += $size;
        }

        // Check total ~ 100, allowing for floating point rounding
        if (abs($memberSize - 100.0) > 0.0001) {
            $fail("The total member size does not equal 100 (got {$memberSize}).");
        }
    }


    public function memberChangeDuplicateValidator($value, $fail): void
    {
        $members = $value['members'];
        $newMembers = $value['new_members'];
        $memberIds = [];

        foreach ($members as $member) {
            $memberIds[] = $member['id_number'];
        }

        foreach ($newMembers as $member) {
            $memberIds[] = $member['id_number'];
        }
        // Check for duplicates
        $counts = array_count_values($memberIds);
        $duplicates = array_filter($counts, function ($count) {
            return $count > 1;
        });

        if (!empty($duplicates)) {
            $fail("Duplicate members found");
        }
    }


    public function bodyParameters(): array
    {
        return array_merge($this->getCipcBodyParameters(), [
            'query_data' => [
                'description' => 'The query data containing member information',
                'required' => true,
                'type' => 'object'
            ],
            'query_data.members' => [
                'description' => 'Array of existing members to be updated',
                'required' => true,
                'type' => 'array'
            ],
            'query_data.members.*.id_number' => [
                'description' => 'Member ID number (ID number for citizens, passport number for non-citizens)',
                'example' => '8001011234081',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.first_name' => [
                'description' => 'Member first name',
                'example' => 'John',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.second_name' => [
                'description' => 'Member second name',
                'example' => 'James',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.members.*.last_name' => [
                'description' => 'Member last name',
                'example' => 'Smith',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.status' => [
                'description' => 'Member status (Active, Deceased, Resigned, Disqualified, Remove, Retired)',
                'example' => 'Active',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.resignation_date' => [
                'description' => 'Date of resignation (required if status is Resigned)',
                'example' => '2024-03-20',
                'required' => false,
                'type' => 'string',
                'format' => 'date'
            ],
            'query_data.members.*.citizen' => [
                'description' => 'Whether the member is a citizen',
                'example' => true,
                'required' => true,
                'type' => 'boolean'
            ],
            'query_data.members.*.country_of_origin' => [
                'description' => 'Member country of origin',
                'example' => 'South Africa',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.phone' => [
                'description' => 'Member phone number',
                'example' => '0112345678',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.email' => [
                'description' => 'Member email address',
                'example' => '<EMAIL>',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.date_of_birth' => [
                'description' => 'Member date of birth (required for non-citizens)',
                'example' => '1980-01-01',
                'required' => false,
                'type' => 'string',
                'format' => 'date'
            ],
            'query_data.members.*.res_address_1' => [
                'description' => 'Member residential address line 1',
                'example' => '123 Main St',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.res_address_2' => [
                'description' => 'Member residential address line 2',
                'example' => 'Sandton',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.members.*.res_city' => [
                'description' => 'Member residential address city',
                'example' => 'Johannesburg',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.res_province' => [
                'description' => 'Member residential address province',
                'example' => 'Gauteng',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.res_postal_code' => [
                'description' => 'Member residential address postal code',
                'example' => '2191',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.pos_address_1' => [
                'description' => 'Member postal address line 1',
                'example' => '123 Main St',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.pos_address_2' => [
                'description' => 'Member postal address line 2',
                'example' => 'Sandton',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.members.*.pos_city' => [
                'description' => 'Member postal address city',
                'example' => 'Johannesburg',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.pos_province' => [
                'description' => 'Member postal address province',
                'example' => 'Gauteng',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.pos_postal_code' => [
                'description' => 'Member postal address postal code',
                'example' => '2191',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.members.*.member_size' => [
                'description' => 'Member size (must be greater than 0)',
                'example' => 50,
                'required' => true,
                'type' => 'number'
            ],
            'query_data.members.*.member_contribution' => [
                'description' => 'Member contribution amount',
                'example' => 1000.00,
                'required' => true,
                'type' => 'number'
            ],
            'query_data.new_members' => [
                'description' => 'Array of new members to be added',
                'required' => false,
                'type' => 'array'
            ],
            'query_data.new_members.*.id_number' => [
                'description' => 'New member ID number (ID number for citizens, passport number for non-citizens)',
                'example' => '8001011234081',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.first_name' => [
                'description' => 'New member first name',
                'example' => 'John',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.second_name' => [
                'description' => 'New member second name',
                'example' => 'James',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.new_members.*.last_name' => [
                'description' => 'New member last name',
                'example' => 'Smith',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.citizen' => [
                'description' => 'Whether the new member is a citizen',
                'example' => true,
                'required' => true,
                'type' => 'boolean'
            ],
            'query_data.new_members.*.country_of_origin' => [
                'description' => 'New member country of origin',
                'example' => 'South Africa',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.appointment_date' => [
                'description' => 'New member appointment date (must be today or a future date)',
                'example' => '2024-03-20',
                'required' => true,
                'type' => 'string',
                'format' => 'date'
            ],
            'query_data.new_members.*.phone' => [
                'description' => 'New member phone number',
                'example' => '0112345678',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.email' => [
                'description' => 'New member email address',
                'example' => '<EMAIL>',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.date_of_birth' => [
                'description' => 'New member date of birth',
                'example' => '1980-01-01',
                'required' => true,
                'type' => 'string',
                'format' => 'date'
            ],
            'query_data.new_members.*.res_address_1' => [
                'description' => 'New member residential address line 1',
                'example' => '123 Main St',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.res_address_2' => [
                'description' => 'New member residential address line 2',
                'example' => 'Sandton',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.new_members.*.res_city' => [
                'description' => 'New member residential address city',
                'example' => 'Johannesburg',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.res_province' => [
                'description' => 'New member residential address province',
                'example' => 'Gauteng',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.res_postal_code' => [
                'description' => 'New member residential address postal code',
                'example' => '2191',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.pos_address_1' => [
                'description' => 'New member postal address line 1',
                'example' => '123 Main St',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.pos_address_2' => [
                'description' => 'New member postal address line 2',
                'example' => 'Sandton',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.new_members.*.pos_city' => [
                'description' => 'New member postal address city',
                'example' => 'Johannesburg',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.pos_province' => [
                'description' => 'New member postal address province',
                'example' => 'Gauteng',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.pos_postal_code' => [
                'description' => 'New member postal address postal code',
                'example' => '2191',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.new_members.*.member_size' => [
                'description' => 'New member size (must be greater than 0)',
                'example' => 50,
                'required' => true,
                'type' => 'integer'
            ],
            'query_data.new_members.*.member_contribution' => [
                'description' => 'New member contribution amount',
                'example' => 1000.00,
                'required' => true,
                'type' => 'number'
            ]
        ]);
    }
}
