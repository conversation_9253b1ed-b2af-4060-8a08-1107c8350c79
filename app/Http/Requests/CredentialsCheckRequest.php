<?php

namespace App\Http\Requests;


use Illuminate\Foundation\Http\FormRequest;

/**
 * Validation rules for the request.
 */
class CredentialsCheckRequest extends CipcBaseRequest
{


    public function authorize()
    {
        return true; // Adjust as necessary
    }

    public function rules()
    {
        return
            [
                'reference' => 'required',
                'customer_code' => 'required|string',
                'customer_password' => 'required|string',
            ];
    }

    public function bodyParameters(): array
    {
        return [
            'reference' => [
                'description' => 'The reference number.',
                'example' => 'ID123'
            ],
            'customer_code' => [
                'description' => 'The CIPC customer code.',
                'example' => 'CUST001'
            ],
            'customer_password' => [
                'description' => 'The CIPC customer password.',
                'example' => 'pass123'
            ],
        ];
    }
}
