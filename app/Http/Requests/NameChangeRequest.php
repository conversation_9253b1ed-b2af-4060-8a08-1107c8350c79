<?php

namespace App\Http\Requests;


use Illuminate\Foundation\Http\FormRequest;

//Breaking Changes
class NameChangeRequest extends CipcCompanyBaseRequest
{


    public function authorize()
    {
        return true; // Adjust as necessary
    }

    public function rules()
    {
        return [
            'reference' => 'required',
            'customer_code' => 'required|string',
            'customer_password' => 'required|string',
            'query_data' => 'required|array',
            'query_data.company' => 'required|array',
            'query_data.company.registration_number' => [
                'required',
                'string',
                'regex:/^[0-9]{4}\/\d{6}\/\d{2}$/',
                function ($attribute, $value, $fail) {
                    if (str_ends_with($value, '23')) {
                        $fail('Name changes cannot be filed for close corporations (CC).');
                    }
                },
            ],
            'query_data.directors' => 'required|array',
            'query_data.directors.*.individual' => 'required|array',
            'query_data.directors.*.individual.id_number' => [
                'required',
                'string',
                'regex:/^[0-9]{13}$/',
            ],
            'query_data.name_reservation' => 'required|array',
            'query_data.name_reservation.tracking_no' => 'required|string',
        ];
    }

    public function messages(): array
    {
        return [
            'reference.required' => 'The transaction ID is required.',
            'customer_code.required' => 'The customer code is required.',
            'customer_password.required' => 'The customer password is required.',

            'query_data.required' => 'The query data is required.',
            'query_data.array' => 'The query data must be an array.',

            'query_data.company.required' => 'The company data is required.',
            'query_data.company.array' => 'The company data must be an array.',
            'query_data.company.registration_number.required' => 'The company registration number is required.',
            'query_data.company.registration_number.string' => 'The company registration number must be text.',
            'query_data.company.registration_number.regex' => 'The company registration number must be in the format YYYY/XXXXXX/ZZ.',

            'query_data.directors.required' => 'The directors data is required.',
            'query_data.directors.array' => 'The directors data must be an array.',
            'query_data.directors.*.individual.required' => 'The director individual data is required.',
            'query_data.directors.*.individual.array' => 'The director individual data must be an array.',
            'query_data.directors.*.individual.id_number.required' => 'The director ID number is required.',
            'query_data.directors.*.individual.id_number.string' => 'The director ID number must be text.',
            'query_data.directors.*.individual.id_number.regex' => 'The director ID number must be 13 digits.',

            'query_data.name_reservation.required' => 'The name reservation data is required.',
            'query_data.name_reservation.array' => 'The name reservation data must be an array.',
            'query_data.name_reservation.tracking_no.required' => 'The name reservation tracking number is required.',
            'query_data.name_reservation.tracking_no.string' => 'The name reservation tracking number must be text.',
        ];
    }

    public function bodyParameters()
    {
        return [
            'reference' => [
                'description' => 'The transaction ID for this request',
                'example' => '12345',
                'required' => true,
                'type' => 'string'
            ],
            'customer_code' => [
                'description' => 'The customer code for authentication',
                'example' => 'CUST001',
                'required' => true,
                'type' => 'string'
            ],
            'customer_password' => [
                'description' => 'The customer password for authentication',
                'example' => 'password123',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.company.registration_number' => [
                'description' => 'The company registration number.',
                'example' => '2020/123456/07',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.directors.*.individual.id_number' => [
                'description' => 'South African ID number of existing director.',
                'example' => '8001015009087',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.name_reservation.tracking_no' => [
                'description' => 'Name reservation tracking number.',
                'example' => '9421234567',
                'required' => true,
                'type' => 'string'
            ]
        ];
    }
}
