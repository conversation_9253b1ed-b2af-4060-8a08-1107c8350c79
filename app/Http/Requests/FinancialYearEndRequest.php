<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FinancialYearEndRequest extends CipcBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // Adjust this based on your authorization logic.
        // For now, we'll allow all requests.
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array_merge($this->getCipcRules(), [
            'query_data.registration_number' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    // Extract the last 2 digits
                    preg_match('/\d{2}$/', $value, $matches);
                    if (!empty($matches) && in_array($matches[0], ['24', '25', '26'])) {
                        $fail("The {$attribute} cannot end with 24, 25, or 26.");
                    }
                }
            ],
            'query_data.financial_year_end' => [
                'required',
                'date',
                'date_format:Y-m-d',
                function ($attribute, $value, $fail) {
                    $date = \DateTime::createFromFormat('Y-m-d', $value);
                    if (!$date) {
                        $fail('The financial year end must be a valid date in YYYY-MM-DD format.');
                        return;
                    }

                    // Check if the date is in the past
                    $today = new \DateTime();
                    if ($date < $today) {
                        $fail('You cannot backdate the financial year end.');
                    }
                }
            ],
        ]);
    }

    public function messages(): array
    {
        return [
            'query_data.registration_number.required' => 'The registration number is required.',
            'query_data.registration_number.string' => 'The registration number must be text.',
            'query_data.registration_number.regex' => 'The registration number must be in the format YYYY/XXXXXX/ZZ.',

            'query_data.financial_year_end.required' => 'The financial year end date is required.',
            'query_data.financial_year_end.date' => 'The financial year end must be a valid date.',
            'query_data.financial_year_end.date_format' => 'The financial year end must be in YYYY-MM-DD format.',
        ];
    }

    public function bodyParameters(): array
    {
        return [
            'reference' => [
                'description' => 'The transaction ID for this request',
                'example' => '12345',
                'required' => true,
                'type' => 'string'
            ],
            'customer_code' => [
                'description' => 'The customer code for authentication',
                'example' => 'CUST001',
                'required' => true,
                'type' => 'string'
            ],
            'customer_password' => [
                'description' => 'The customer password for authentication',
                'example' => 'password123',
                'required' => true,
                'type' => 'string'
            ],
            'query_data' => [
                'description' => 'The query data containing company information',
                'required' => true,
                'type' => 'object'
            ],
            'query_data.registration_number' => [
                'description' => 'The company registration number in format YYYY/XXXXXX/ZZ (cannot end with 24, 25, or 26)',
                'example' => '2020/123456/07',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.financial_year_end' => [
                'description' => 'The new financial year end date in YYYY-MM-DD format (must be today or a future date)',
                'example' => '2024-12-31',
                'required' => true,
                'type' => 'string',
                'format' => 'date'
            ]
        ];
    }
}
