<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NameReservationRequest extends CipcCompanyBaseRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return array_merge($this->getCipcRules(), [
            'query_data.proposed_name_1' => [
                'required',
                'string',
                'min:2',
                'max:100',
                function ($attribute, $value, $fail) {
                    // Check for invalid characters
                    if (preg_match('~[<>{}[\]\\/]~', $value)) {
                        $fail('The company name cannot contain special characters like < > { } [ ] \\ /');
                    }
                    // Check for consecutive spaces
                    if (preg_match('/\s{2,}/', $value)) {
                        $fail('The company name cannot contain consecutive spaces');
                    }
                    // Check for leading/trailing spaces
                    if (trim($value) !== $value) {
                        $fail('The company name cannot have leading or trailing spaces');
                    }
                }
            ],
            'query_data.proposed_name_2' => [
                'nullable',
                'string',
                'min:2',
                'max:100',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        // Check for invalid characters
                        if (preg_match('~[<>{}[\]\\/]~', $value)) {
                            $fail('The company name cannot contain special characters like < > { } [ ] \\ /');
                        }
                        // Check for consecutive spaces
                        if (preg_match('/\s{2,}/', $value)) {
                            $fail('The company name cannot contain consecutive spaces');
                        }
                        // Check for leading/trailing spaces
                        if (trim($value) !== $value) {
                            $fail('The company name cannot have leading or trailing spaces');
                        }
                    }
                }
            ],
            'query_data.proposed_name_3' => [
                'nullable',
                'string',
                'min:2',
                'max:100',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        // Check for invalid characters
                        if (preg_match('~[<>{}[\]\\/]~', $value)) {
                            $fail('The company name cannot contain special characters like < > { } [ ] \\ /');
                        }
                        // Check for consecutive spaces
                        if (preg_match('/\s{2,}/', $value)) {
                            $fail('The company name cannot contain consecutive spaces');
                        }
                        // Check for leading/trailing spaces
                        if (trim($value) !== $value) {
                            $fail('The company name cannot have leading or trailing spaces');
                        }
                    }
                }
            ],
            'query_data.proposed_name_4' => [
                'nullable',
                'string',
                'min:2',
                'max:100',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        // Check for invalid characters
                        if (preg_match('~[<>{}[\]\\/]~', $value)) {
                            $fail('The company name cannot contain special characters like < > { } [ ] \\ /');
                        }
                        // Check for consecutive spaces
                        if (preg_match('/\s{2,}/', $value)) {
                            $fail('The company name cannot contain consecutive spaces');
                        }
                        // Check for leading/trailing spaces
                        if (trim($value) !== $value) {
                            $fail('The company name cannot have leading or trailing spaces');
                        }
                    }
                }
            ],
        ]);
    }

    public function messages(): array
    {
        return [
            'reference.required' => 'The transaction ID is required.',
            'customer_code.required' => 'The customer code is required.',
            'customer_code.string' => 'The customer code must be text.',
            'customer_password.required' => 'The customer password is required.',
            'customer_password.string' => 'The customer password must be text.',
            'query_data.required' => 'The query data is required.',
            'query_data.array' => 'The query data must be an array.',

            'query_data.proposed_name_1.required' => 'At least one company name is required.',
            'query_data.proposed_name_1.string' => 'The company name must be text.',
            'query_data.proposed_name_1.min' => 'The company name must be at least 2 characters.',
            'query_data.proposed_name_1.max' => 'The company name cannot exceed 100 characters.',

            'query_data.proposed_name_2.string' => 'The alternative company name must be text.',
            'query_data.proposed_name_2.min' => 'The alternative company name must be at least 2 characters.',
            'query_data.proposed_name_2.max' => 'The alternative company name cannot exceed 100 characters.',

            'query_data.proposed_name_3.string' => 'The alternative company name must be text.',
            'query_data.proposed_name_3.min' => 'The alternative company name must be at least 2 characters.',
            'query_data.proposed_name_3.max' => 'The alternative company name cannot exceed 100 characters.',

            'query_data.proposed_name_4.string' => 'The alternative company name must be text.',
            'query_data.proposed_name_4.min' => 'The alternative company name must be at least 2 characters.',
            'query_data.proposed_name_4.max' => 'The alternative company name cannot exceed 100 characters.',
        ];
    }

    public function bodyParameters(): array
    {
        return [
            'reference' => [
                'description' => 'The transaction ID for this request',
                'example' => '12345',
                'required' => true,
                'type' => 'string'
            ],
            'customer_code' => [
                'description' => 'The customer code for authentication',
                'example' => 'CUST001',
                'required' => true,
                'type' => 'string'
            ],
            'customer_password' => [
                'description' => 'The customer password for authentication',
                'example' => 'password123',
                'required' => true,
                'type' => 'string'
            ],
            'query_data' => [
                'description' => 'The query data containing proposed company names',
                'required' => true,
                'type' => 'object'
            ],
            'query_data.proposed_name_1' => [
                'description' => 'Primary proposed company name (2-100 characters, no special characters or consecutive spaces)',
                'example' => 'Acme Corporation',
                'required' => true,
                'type' => 'string'
            ],
            'query_data.proposed_name_2' => [
                'description' => 'First alternative company name (2-100 characters, no special characters or consecutive spaces)',
                'example' => 'Acme Corp',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.proposed_name_3' => [
                'description' => 'Second alternative company name (2-100 characters, no special characters or consecutive spaces)',
                'example' => 'Acme Group',
                'required' => false,
                'type' => 'string'
            ],
            'query_data.proposed_name_4' => [
                'description' => 'Third alternative company name (2-100 characters, no special characters or consecutive spaces)',
                'example' => 'Acme Holdings',
                'required' => false,
                'type' => 'string'
            ]
        ];
    }
}
