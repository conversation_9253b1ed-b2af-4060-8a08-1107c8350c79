<?php

namespace App\Http\Requests;


use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DirectorChangeVerifyRequest extends FormRequest
{


    public function authorize()
    {
        return true; // Adjust as necessary
    }

    public function rules()
    {
        return [
            // Validation rules for 'cipc_transaction'
            'cipc_transaction' => 'required|array',
            'cipc_transaction.id' => 'required|numeric',
            'cipc_transaction.tracking_no' => 'required|string',

            // Validation rules for 'directors'
            'directors' => 'required|array',
            'directors.*.full_name' => 'required|string',
            'directors.*.id_number' => 'required|string',
            'directors.*.otps' => 'required|array',
            'directors.*.otps.*' => 'string',
        ];
    }
}
