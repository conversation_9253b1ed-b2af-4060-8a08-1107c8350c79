<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SubmissionsCallbackRequest extends FormRequest
{
    public function authorize()
    {
        return true; // Adjust if needed
    }

    public function rules()
    {
        return [
            'statusCode' => 'required|integer',
            'transactionId' => 'required',
            'body' => 'required|array',
            'isBase64Encoded' => 'required|boolean',
            'local' => 'nullable',
            'aws_request_id' => 'nullable',
        ];
    }
}
