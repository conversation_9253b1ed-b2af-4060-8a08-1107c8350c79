<?php

namespace App\Http\Requests;


use Illuminate\Foundation\Http\FormRequest;

class AnnualReturnScraperRequest extends FormRequest
{


    public function authorize()
    {
        return true; // Adjust as necessary
    }

    public function rules()
    {
        $rules = [
            'reference' => 'required',
            'customer_code' => 'required|string',
            'customer_password' => 'required|string',
            'query_data' => 'required|array',
            'query_data.registration_numbers' => 'required|array|min:1|max:10',
            'query_data.registration_numbers.*' => [
                'required',
                'regex:/^\d{4}\/\d{6}\/\d{2}$/',
            ],


        ];

        return $rules;
    }
}
