<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ComplianceChecklistRequest extends CipcCompanyBaseRequest
{
    public function authorize()
    {
        return true; // Adjust as necessary
    }

    public function rules()
    {
        $rules = [
            'reference' => 'required',
            'customer_code' => 'required|string',
            'customer_password' => 'required|string',

            'query_data' => 'required|array',
            // Validation rules for 'query_data.company'
            'query_data.company.registration_number' => 'required|string',

            // Validation rule for 'query_data.audit'
            'query_data.audit' => 'required|integer',

            // Validation rules for 'query_data.checklist'
            'query_data.checklist.reasons' => 'nullable',
        ];

        return $rules;
    }

    public function bodyParameters(): array
    {
        return array_merge($this->getCompanyBodyParameters(), [
            'query_data' => [
                'description' => 'The query data.',
                'example' => [
                    'company' => [
                        'registration_number' => '12345'
                    ],
                    'audit' => [
                        'audit_type' => '1'
                    ],
                    'checklist' => [
                        'reasons' => '1'
                    ] // NOTE: Get all required fields for checklist from CIPC API documentation
                ]
            ]
        ]);
    }
}
