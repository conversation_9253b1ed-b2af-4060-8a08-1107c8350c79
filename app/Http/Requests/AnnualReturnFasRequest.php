<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;

class AnnualReturnFasRequest extends CipcCompanyBaseRequest
{


    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return array_merge($this->getCompanyRules(), [

            // **COMPANY INFORMATION**
            'query_data.company.phone' => [
                'required',
                'string',
            ],
            'query_data.company.email' => 'required|email',
            'query_data.company.company_type_id' => [
                'required',
                Rule::in([3, 4, 5, 6, 9, 10, 21]) // pty, cc, npc, plc
            ],
            'query_data.company.industry' => [
                'required',
                'string',
                Rule::in([
                    "Agriculture, forestry and fishing",
                    "Mining and quarrying",
                    "Manufacturing",
                    "Electricity, gas, steam and air conditioning supply",
                    "Water supply; sewerage, waste management and remediation activities",
                    "Construction",
                    "Wholesale and retail trade; repair of motor vehicles and motorcycles",
                    "Transportation and storage",
                    "Accommodation and food service activities",
                    "Information and communication",
                    "Financial and insurance activities",
                    "Real estate activities",
                    "Professional, scientific and technical activities",
                    "Administrative and support service activities",
                    "Public administration and defence; compulsary social security",
                    "Education",
                    "Human health and social work activities",
                    "Arts, entertainment and recreation",
                    "Other service activities",
                    "Activities of households as employers",
                    "Activities of extraterritorial organisations and bodies, not economically active people, etc."
                ])
            ],
            'query_data.company.website' => 'nullable|string',
            'query_data.company.business_description' => 'nullable|string',

            // **PERIODS**
            'query_data.periods' => 'required',
            'query_data.periods.*.turnover' => 'required|numeric', // Accepts number or numeric string

            // **FINANCIAL FORMAT**
            'query_data.fin_format' => [
                'required',
                'string',
                Rule::in([
                    'Electronically-computer based system',
                    'Manually, in paper based records'
                ])
            ],

            // **ASSET HOLDING DECLARATION**
            'query_data.holding_assets' => 'required|boolean',

            // **ACCOUNTING OFFICER INFORMATION**
            'query_data.accounting' => 'required|array',
            'query_data.accounting.type' => [
                'required',
                'string',
                Rule::in(['1', '2']) // 1 for natural person, 2 for juristic person
            ],
            'query_data.accounting.name' => [
                'required_if:query_data.accounting.type,1',
                'string'
            ],
            'query_data.accounting.id_number' => [
                'required_if:query_data.accounting.type,1',
                'string'
            ],
            'query_data.accounting.citizen' => [
                'required_if:query_data.accounting.type,1',
                'boolean'
            ],
            'query_data.accounting.country' => [
                function ($attribute, $value, $fail) {
                    $type = request()->input('query_data.accounting.type');
                    $citizen = request()->input('query_data.accounting.citizen');

                    if ($type === '1' && $citizen === false && empty($value)) {
                        $fail('The country field is required when the accounting officer is a natural person and not a citizen.');
                    }
                },
                'nullable',
                'string'
            ],
            'query_data.accounting.dob' => [
                'required_if:query_data.accounting.type,1',
                'nullable',
                'string'
            ],

            // **FINANCIAL STATEMENTS**
            'query_data.financials' => 'required|array',
            'query_data.financials.type' => [
                'required',
                'string',
                Rule::in(['1', '2', '100']) // 1 for natural person, 2 for juristic person, 100 for no financials
            ],
            'query_data.financials.name' => [
                'required_if:query_data.financials.type,1',
                'nullable',
                'string'
            ],
            'query_data.financials.citizen' => [
                'required_if:query_data.financials.type,1',
                'boolean'
            ],
            'query_data.financials.id_number' => [
                'required_if:query_data.financials.type,1',
                'nullable',
                'string',
            ],
            'query_data.financials.country' => [
                'required_if:query_data.financials.citizen,false',
                'nullable',
                'string'
            ],
            'query_data.financials.dob' => [
                'required_if:query_data.financials.citizen,false',
                'nullable',
                'string',
            ],

            // **FINANCIAL ADVISOR**
            'query_data.fin_advisor' => 'required|array',
            'query_data.fin_advisor.checkbox' => [
                'nullable',
                Rule::in(['0', '1', 'true', 'false', true, false])
            ],
            'query_data.fin_advisor.name' => 'required_if:query_data.fin_advisor.checkbox,1,true|nullable|string',

            // **INDEPENDENT REVIEW**
            'query_data.ind_review' => 'required|array',
            'query_data.ind_review.checkbox' => 'nullable',
            'query_data.ind_review.name' => 'required_if:query_data.ind_review.checkbox,1,true|nullable|string',
            'query_data.ind_review.profession' => [
                'required_if:query_data.ind_review.checkbox,1,true',
                'nullable',
                'string',
                Rule::in([
                    'SAICA',
                    'IRBA',
                    'ICSA',
                    'CIMA',
                    'SAIPA',
                    'IAC',
                    'SAIGA',
                    'ACCA',
                    'MCIBM',
                    'SAIBA'
                ])
            ],
            'query_data.ind_review.profession_no' => 'required_if:query_data.ind_review.checkbox,1,true|nullable|string',

            // **STOCKTAKES**
            'query_data.stocktakes' => 'required|array',
            'query_data.stocktakes.checkbox' => 'nullable',
            'query_data.stocktakes.frequency' => [
                'required_if:query_data.stocktakes.checkbox,1,true',
                'nullable',
                'string',
                Rule::in(['Monthly', 'Quarterly', 'Semi-Annually', 'Annually', 'Never'])
            ]
        ]);
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages()
    {
        return [
            'query_data.company.phone.regex' => 'The phone number must be 10 digits.',
            'query_data.company.company_type_id.in' => 'The company type must be either pty (3), cc (4), or npc (5).',
            'query_data.periods.*.turnover.regex' => 'The turnover must be a valid amount (e.g., R1000 or 1000).',
            'query_data.accounting.id_number.regex' => 'The ID number must be either a 13-digit number or a company registration number in the format YYYY/XXXXXX/YY.',
            'query_data.accounting.dob.regex' => 'The date of birth must be in the format YYYY-MM-DD.',
            'query_data.financials.id_number.regex' => 'The ID number must be either a 13-digit number or a company registration number in the format YYYY/XXXXXX/YY.',
            'query_data.financials.dob.regex' => 'The date of birth must be in the format YYYY-MM-DD.',
        ];
    }

    /**
     * Get the body parameters for the request.
     *
     * @return array<string, array>
     */
    public function bodyParameters(): array
    {
        return array_merge($this->getCompanyBodyParameters(), [
            'query_data.company.phone' => [
                'description' => 'The company phone number (10 digits).',
                'example' => '**********'
            ],
            'query_data.company.email' => [
                'description' => 'The company email address.',
                'example' => '<EMAIL>'
            ],
            'query_data.company.company_type_id' => [
                'description' => 'The company type ID (3 for pty, 4 for cc, 5 for npc, 9 for plc).',
                'example' => '3'
            ],
            'query_data.company.industry' => [
                'description' => 'The company industry sector.'
            ],
            'query_data.company.website' => [
                'description' => 'The company website URL.',
                'example' => 'https://www.example.com'
            ],
            'query_data.company.business_description' => [
                'description' => 'A brief description of the company\'s business activities.',
                'example' => 'Retail store specializing in electronics'
            ],
            'query_data.periods.*.turnover' => [
                'description' => 'The turnover amount for each period (can include R prefix).',
                'example' => 'R1000000'
            ],
            'query_data.fin_format' => [
                'description' => 'The format of financial records.',
                'example' => 'Electronically-computer based system'
            ],
            'query_data.holding_assets' => [
                'description' => 'Whether the company holds assets.',
                'example' => true
            ],
            'query_data.accounting.type' => [
                'description' => 'The type of accounting officer (1 for natural person, 2 for juristic person).',
                'example' => '1'
            ],
            'query_data.accounting.name' => [
                'description' => 'The name of the accounting officer.',
                'example' => 'John Smith'
            ],
            'query_data.accounting.id_number' => [
                'description' => 'The ID number or company registration number of the accounting officer.',
                'example' => '*************'
            ],
            'query_data.accounting.citizen' => [
                'description' => 'Whether the accounting officer is a South African citizen.',
                'example' => true
            ],
            'query_data.accounting.country' => [
                'description' => 'The country of origin for non-citizen accounting officers.',
                'example' => 'United Kingdom'
            ],
            'query_data.accounting.dob' => [
                'description' => 'The date of birth for non-citizen accounting officers (YYYY-MM-DD).',
                'example' => '1980-01-01'
            ]
        ]);
    }
}
