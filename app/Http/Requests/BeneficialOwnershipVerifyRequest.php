<?php

namespace App\Http\Requests;

use App\Rules\IntegerOrBooleanRule;
use App\Rules\StringOrIntegerRule;


class BeneficialOwnershipVerifyRequest extends CipcCompanyBaseRequest
{


    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return array_merge($this->getCipcRules(), [
            'query_data.otps' => 'array',
            'query_data.tracking_no' => 'required|string'
        ]);
    }

    /**
     * Descriptions and examples of the body parameters for the request.
     */
    public function bodyParameters(): array
    {
        return array_merge($this->getCipcBodyParameters(), [

            'Query_Data.otps' => [
                'description' => 'Array of OTPs for verification',
                'example' => []
            ],
            'Query_Data.company_id' => [
                'description' => 'Company identifier',
                'example' => 56278
            ],
            'Query_Data.tracking_no' => [
                'description' => 'Tracking number for the verification request',
                'example' => '60006011882'
            ]
        ]);
    }
}
