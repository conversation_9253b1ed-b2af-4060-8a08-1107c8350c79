<?php

namespace App\Http\Requests;

class CipcBaseRequest extends SubmissionBaseRequest
{
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to all CIPC requests.
     */
    protected function getCipcRules(): array
    {
        return array_merge($this->getBaseRules(), [
            'customer_code' => 'required|string',
            'customer_password' => 'required|string',
        ]);
    }

    /**
     * Get the body parameter descriptions that apply to all CIPC requests.
     */
    protected function getCipcBodyParameters(): array
    {
        return array_merge($this->getBaseBodyParameters(), [
            'customer_code' => [
                'description' => 'The CIPC customer code.',
                'example' => 'CUST001'
            ],
            'customer_password' => [
                'description' => 'The CIPC customer password.',
                'example' => 'pass123'
            ],
        ]);
    }
}
