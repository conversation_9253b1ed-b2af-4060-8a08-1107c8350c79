<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

//Breaking Changes
class DocumentRequest extends CipcBaseRequest
{


    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return array_merge($this->getCipcRules(), [

            'query_data.document_type' => [
                'required',
                'string',
                'in:CIPC Official Disclosure,Incorporation Documents (MOI)'
            ],
            'query_data.registration_number' => [
                'required',
                'string',
                'regex:/^[0-9]{4}\/[0-9]{6}\/[0-9]{2}$/'
            ],
        ]);
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages()
    {
        return [
            'query_data.document_type.in' => 'The document type must be either "CIPC Official Disclosure" or "Incorporation Documents (MOI)".',
            'query_data.registration_number.regex' => 'The registration number must be in the format YYYY/XXXXXX/YY (e.g., 2020/123456/07).',
        ];
    }

    /**
     * Get the body parameters for the request.
     *
     * @return array<string, array>
     */
    public function bodyParameters(): array
    {
        return array_merge($this->getCipcBodyParameters(), [
            'query_data.document_type' => [
                'description' => 'The type of document to request. Must be either "CIPC Official Disclosure" or "Incorporation Documents (MOI)".',
                'example' => 'CIPC Official Disclosure'
            ],
            'query_data.registration_number' => [
                'description' => 'The company registration number in the format YYYY/XXXXXX/YY.',
                'example' => '2020/123456/07'
            ],
        ]);
    }
}
