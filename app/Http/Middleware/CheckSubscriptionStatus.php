<?php

namespace App\Http\Middleware;

use App\Models\Subscription;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class CheckSubscriptionStatus
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): JsonResponse
    {
        if (Auth::check()) {
            $account = Auth::user()->account;

            $subscription = $account->subscriptions()->latest()->first();

            if (!$subscription) {
                return response()->json(['message' => 'No subscription found.'], Response::HTTP_FORBIDDEN);
            }

            $this->incrementRateUsage($subscription);

            if ($this->subscriptionExpired($subscription)) {
                return response()->json(['message' => 'Subscription expired.'], Response::HTTP_FORBIDDEN);
            }

            if ($this->exceededRateLimit($subscription)) {
                return response()->json(['message' => 'Rate limit exceeded.'], Response::HTTP_TOO_MANY_REQUESTS);
            }
        }

        return $next($request);
    }

    private function exceededRateLimit(Subscription $subscription): bool
    {
        if ($subscription->subscriptionPlan->rate_limit === 0) {
            return false;
        }

        return $subscription->rate_usage > $subscription->subscriptionPlan->rate_limit;
    }

    private function subscriptionExpired(Subscription $subscription): bool
    {
        return !empty($subscription->end_date) && Carbon::today()->greaterThan(Carbon::parse($subscription->end_date));
    }

    private function incrementRateUsage(Subscription $subscription): void
    {
        $subscription->increment('rate_usage');
    }
}
