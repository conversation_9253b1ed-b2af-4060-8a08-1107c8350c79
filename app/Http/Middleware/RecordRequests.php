<?php

namespace App\Http\Middleware;

use App\Enums\EndpointEnum;
use App\Enums\RequestLogStatusEnum;
use App\Enums\StatusEnum;
use App\Models\RequestLog;
use App\Models\Subscription;
use App\Models\CipcCustomer;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class RecordRequests
{
    protected array $routes = [
        '/api/v1/cipc/*',
        '/api/v1/director-change-verify',
        '/api/v1/director-change',
    ];
    protected array $explicitExcept = [
        '/api/v1/cipc/foreigner-assurance/verify',
    ];

    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): JsonResponse
    {
        if ($this->inRoutesArray($request) && !$this->inExceptArray($request)) {
            // Check for customer code and password in request
            if (!empty($request->customer_code) && !empty($request->customer_password)) {
                $accountId = $request->user()->account_id;

                // Check if customer exists
                $customer = CipcCustomer::where('account_id', $accountId)
                    ->where('code', $request->customer_code)
                    ->first();

                // If customer doesn't exist, create new record
                if (!$customer) {
                    CipcCustomer::create([
                        'account_id' => $accountId,
                        'code' => $request->customer_code,
                        'password' => $request->customer_password,
                        'balance' => 0.0,
                        'auth_url' => null
                    ]);
                }
            }

            $path = Str::replace('api/v1/', '', $request->path());
            $endpointId = EndpointEnum::getEndpointIdFromSubmissionsEndpoint($path);
            $reference = $request->reference;
            if ($request->transactionId) {
                $reference = $request->transactionId;
            }
            $requestLog = RequestLog::create([
                'personal_access_token_id' => $request->user()->currentAccessToken()->id,
                'endpoint_id' => $endpointId,
                'account_id' => $request->user()->account_id,
                'reference' => $reference,
                'tracking_no' => $request->tracking_no,
                'is_test' => $request->is_test ? 1 : 0,
                'ip_address' => $request->ip(),
                'request_body' => $request->all(),
                'response_body' => json_encode([]),
            ]);
            $request->attributes->set('request_log_id', $requestLog->id);
        }
        return tap($next($request), function ($response) use ($request) {
            $requestLogId = $request->attributes->get('request_log_id');
            if ($requestLogId) {
                RequestLog::where('id', $requestLogId)->update([
                    'status_code' => $response->getStatusCode(),
                    'response_body' => $response->getContent(),
                ]);
            }
        });
    }

    protected function inRoutesArray($request): bool
    {
        foreach ($this->routes as $route) {
            if ($route !== '/') {
                $route = trim($route, '/');
            }

            if ($request->fullUrlIs($route) || $request->is($route)) {
                return true;
            }
        }

        return false;
    }

    protected function inExceptArray($request): bool
    {
        foreach ($this->explicitExcept as $route) {
            if ($route !== '/') {
                $route = trim($route, '/');
            }

            if ($request->fullUrlIs($route) || $request->is($route)) {
                return true;
            }
        }

        return false;
    }
}
