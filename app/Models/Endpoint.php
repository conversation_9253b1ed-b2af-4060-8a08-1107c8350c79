<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Endpoint extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'url_path',
        'function_name',
        'type',
        'turnaround_time_sec',
        'paid',
        'enabled',
    ];

    protected $casts = [
        'paid' => 'boolean',
        'enabled' => 'boolean',
        'turnaround_time_sec' => 'integer',
    ];
}
