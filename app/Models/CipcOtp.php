<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CipcOtp extends Model
{
    use HasFactory;


    protected $fillable = [
        'request_log_id',
        'otp'
    ];

    public function requestLog(): BelongsTo
    {
        return $this->belongsTo(RequestLog::class);
    }
}
