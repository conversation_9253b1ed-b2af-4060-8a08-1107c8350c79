<?php

namespace App\Models;

use App\Enums\ResponseStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class Webhook extends Model
{
    use HasFactory;

    protected $fillable = [
        'submission_id',
        'url',
        'status_code',
        'retry_count',
        'last_attempt_at',
        'payload',
    ];
    protected $casts = [
        'payload' => 'array',
        'last_attempt_at' => 'datetime',
    ];

    public function submission(): BelongsTo
    {
        return $this->belongsTo(Submission::class);
    }

    public function account(): HasOneThrough
    {
        return $this->hasOneThrough(
            Account::class,
            Submission::class,
            'id', // Foreign key on submissions table
            'id', // Foreign key on accounts table
            'submission_id', // Local key on webhooks table
            'account_id' // Local key on submissions table
        );
    }
}
