<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Laravel\Sanctum\PersonalAccessToken;

class RequestLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'personal_access_token_id',
        'account_id',
        'endpoint_id',
        'user_id',
        'reference',
        'ip_address',
        'status_code',
        'request_body',
        'response_body',
        'tracking_no',
        'is_test',
    ];
    protected $casts = [
        'request_body' => 'array',
        'response_body' => 'array',
        'is_test' => 'boolean',
    ];



    public function status(): BelongsTo
    {
        return $this->belongsTo(Status::class);
    }

    public function endpoint(): BelongsTo
    {
        return $this->belongsTo(Endpoint::class, 'endpoint_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function personalAccessToken(): BelongsTo
    {
        return $this->belongsTo(PersonalAccessToken::class);
    }

    public function scopeFilter($query, array $filters): Builder
    {
        $query->when($filters['search'] ?? null, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('id', 'like', '%' . $search . '%');
                $query->orWhere('reference', 'like', '%' . $search . '%');
                $query->orWhere('request_body', 'like', '%' . $search . '%');
                $query->orWhere('response_body', 'like', '%' . $search . '%');
            });
        });
        $query->when($filters['endpoint_id'] ?? null, function ($query, $search) {
            $query->where('endpoint_id', $search);
        });
        $query->when($filters['account_id'] ?? null, function ($query, $search) {
            $query->where('account_id', $search);
        });
        $query->when($filters['status_code'] ?? null, function ($query, $search) {
            $query->where('status_code', $search);
        });
        $query->when($filters['personal_access_token_id'] ?? null, function ($query, $search) {
            $query->where('personal_access_token_id', $search);
        });
        return $query;
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }
    public function submission(): HasOne
    {
        return $this->hasOne(Submission::class);
    }
    public function otps(): HasMany
    {
        return $this->hasMany(CipcOtp::class);
    }
}
