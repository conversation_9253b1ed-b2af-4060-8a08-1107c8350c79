<?php

namespace App\Models;

use App\Enums\CustomerTypeEnum;
use App\Enums\SubmissionStatusEnum;
use App\Events\SubmissionStatusChangedEvent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Submission extends Model
{
    use HasFactory;

    protected $fillable = [
        'endpoint_id',
        'status',
        'reference',
        'account_id',
        'customer_type',
        'customer_id',
        'json_data',
        'submitted_at',
        'request_log_id',
        'manual_review_notes',
        'retry_count',
    ];

    protected $casts = [
        'status' => SubmissionStatusEnum::class,
        'customer_type' => CustomerTypeEnum::class,
        'json_data' => 'array',
        'submitted_at' => 'datetime',
    ];

    public function endpoint(): BelongsTo
    {
        return $this->belongsTo(Endpoint::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function requestLog(): BelongsTo
    {
        return $this->belongsTo(RequestLog::class);
    }

    public function submissionLogs(): HasMany
    {
        return $this->hasMany(SubmissionLog::class);
    }

    public function webhooks(): HasMany
    {
        return $this->hasMany(Webhook::class);
    }

    protected static function booted(): void
    {
        static::updating(function ($submission) {
            if ($submission->isDirty('status')) { // Check if status is changing
                event(new SubmissionStatusChangedEvent($submission));
                if (
                    $submission->getOriginal('status') === SubmissionStatusEnum::MANUAL_REVIEW &&
                    $submission->status === SubmissionStatusEnum::PENDING
                ) {
                    $submission->retry_count = 0;
                }
            }
        });
    }
}
