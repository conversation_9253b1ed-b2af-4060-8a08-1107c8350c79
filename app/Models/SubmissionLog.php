<?php

namespace App\Models;

use App\Enums\ResponseStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class SubmissionLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'submission_id',
        'url',
        'function_name',
        'status',
        'payload',
        'status_code',
        'duration_ms',
        'response_body',
        'aws_request_id',
    ];
    protected $casts = [
        'status_code' => ResponseStatusEnum::class,
        'payload' => 'array',
        'response_body' => 'array',
    ];

    public function submission(): BelongsTo
    {
        return $this->belongsTo(Submission::class);
    }

    public function endpoint(): HasOneThrough
    {
        return $this->hasOneThrough(
            Endpoint::class,
            Submission::class,
            'id', // Foreign key on submissions table
            'id', // Foreign key on endpoints table
            'submission_id', // Local key on submission_logs table
            'endpoint_id' // Local key on submissions table
        );
    }
}
