<?php

namespace App\Models;

use App\Observers\AccountObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

#[ObservedBy([AccountObserver::class])]
class Account extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'subscription_plan_id',
        'subscription_end_date',
        'webhook_url',
        'webhook_url_headers',
    ];

    protected $casts = [
        'webhook_url_headers' => 'array',
    ];

    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    public function submissions(): HasMany
    {
        return $this->hasMany(Submission::class);
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function webhooks(): HasManyThrough
    {
        return $this->hasManyThrough(Webhook::class, Submission::class);
    }

    public function submissionLogs(): HasManyThrough
    {
        return $this->hasManyThrough(SubmissionLog::class, Submission::class);
    }

    public function cipcCustomers(): HasMany
    {
        return $this->hasMany(CipcCustomer::class);
    }

    public function requestLogs(): HasMany
    {
        return $this->hasMany(RequestLog::class);
    }

    public function scopeFilter(Builder $query, array $filters): Builder
    {
        $query->when($filters['search'] ?? null, function (Builder $query, $search) {
            $query->where(function (Builder $query) use ($search) {
                $query->where('id', 'like', '%' . $search . '%')
                    ->orWhere('name', 'like', '%' . $search . '%');
            });
        });

        $query->when($filters['subscription_plan_id'] ?? null, function (Builder $query, $search) {
            $query->where('subscription_plan_id', $search);
        });

        return $query;
    }
}
