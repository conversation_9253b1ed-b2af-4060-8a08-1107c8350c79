<?php

namespace App\Nova\Metrics;

use App\Models\RequestLog;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Trend;
use Carbon\CarbonImmutable;

class ValidationFailuresTrend extends Trend
{
    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        return $this->countByDays(
            $request,
            RequestLog::query()->where('status_code', 422),
            'created_at'
        );
    }

    /**
     * Get the name of the metric.
     *
     * @return string
     */
    public function name()
    {
        return 'Validation Failures Trend';
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            1 => __('Today'),
            7 => __('This Week'),
            30 => __('This Month'),
            60 => __('This Quarter'),
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'validation-failures-trend';
    }
}
