<?php

namespace App\Nova\Metrics;

use App\Models\Submission;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Value;
use Carbon\CarbonImmutable;
use Illuminate\Support\Facades\DB;

class SubmissionTotalProcessingTime extends Value
{
    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        // Get the start and end dates for the current and previous ranges
        $currentRange = $this->currentRange($request->range, $request->timezone);
        $previousRange = $this->previousRange($request->range, $request->timezone);

        // Calculate total processing time for the current range
        $currentTotal = $this->calculateTotalProcessingTime($currentRange[0], $currentRange[1]);

        // Calculate total processing time for the previous range
        $previousTotal = $this->calculateTotalProcessingTime($previousRange[0], $previousRange[1]);

        // Return the result with the previous value
        return $this->result($currentTotal)
            ->previous($previousTotal);
    }

    /**
     * Calculate the total processing time for submissions within a date range.
     *
     * @param  \Carbon\CarbonImmutable  $startDate
     * @param  \Carbon\CarbonImmutable  $endDate
     * @return int
     */
    protected function calculateTotalProcessingTime(CarbonImmutable $startDate, CarbonImmutable $endDate): int
    {
        return Submission::whereBetween('created_at', [$startDate, $endDate])
            ->whereNotNull('processed_at')
            ->sum(DB::raw('TIMESTAMPDIFF(SECOND, created_at, processed_at)'));
    }

    /**
     * Get the name of the metric.
     *
     * @return string
     */
    public function name()
    {
        return 'Total Processing Time';
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            1 => __('Today'),
            7 => __('This Week'),
            30 => __('This Month'),
            60 => __('This Quarter'),
            365 => __('This Year'),
            'ALL' => __('All Time'),
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'submission-total-processing-time';
    }

    /**
     * Get the current range for the metric.
     *
     * @param  string|int  $range
     * @param  string  $timezone
     * @return array
     */
    protected function currentRange(string|int $range, string $timezone): array
    {
        if ($range === 'ALL') {
            return [
                CarbonImmutable::createFromTimestamp(0, $timezone),
                CarbonImmutable::now($timezone),
            ];
        }

        return parent::currentRange($range, $timezone);
    }

    /**
     * Get the previous range for the metric.
     *
     * @param  string|int  $range
     * @param  string  $timezone
     * @return array
     */
    protected function previousRange(string|int $range, string $timezone): array
    {
        if ($range === 'ALL') {
            return [
                CarbonImmutable::createFromTimestamp(0, $timezone),
                CarbonImmutable::now($timezone),
            ];
        }

        return parent::previousRange($range, $timezone);
    }
}
