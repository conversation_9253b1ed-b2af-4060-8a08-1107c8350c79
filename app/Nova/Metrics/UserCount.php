<?php

namespace App\Nova\Metrics;

use App\Models\User;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Value;
use Carbon\CarbonImmutable;

class UserCount extends Value
{
    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        // Get the start and end dates for the current and previous ranges
        $currentRange = $this->currentRange($request->range, $request->timezone);
        $previousRange = $this->previousRange($request->range, $request->timezone);

        // Calculate the count for the current range
        $currentCount = User::query()
            ->whereBetween('created_at', [$currentRange[0], $currentRange[1]])
            ->count();

        // Calculate the count for the previous range
        $previousCount = User::query()
            ->whereBetween('created_at', [$previousRange[0], $previousRange[1]])
            ->count();

        // Return the result with the previous value
        return $this->result($currentCount)
            ->previous($previousCount);
    }

    /**
     * Get the name of the metric.
     *
     * @return string
     */
    public function name()
    {
        return 'Total Users';
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            1 => __('Today'),
            7 => __('This Week'),
            30 => __('This Month'),
            60 => __('This Quarter'),
            365 => __('This Year'),
            'ALL' => __('All Time'),
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'user-count';
    }

    /**
     * Get the current range for the metric.
     *
     * @param  string|int  $range
     * @param  string  $timezone
     * @return array
     */
    protected function currentRange(string|int $range, string $timezone): array
    {
        if ($range === 'ALL') {
            return [
                CarbonImmutable::createFromTimestamp(0, $timezone),
                CarbonImmutable::now($timezone),
            ];
        }

        return parent::currentRange($range, $timezone);
    }
}
