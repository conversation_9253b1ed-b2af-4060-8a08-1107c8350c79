<?php

namespace App\Nova\Metrics\CompanyRegistration;

use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Trend;
use <PERSON><PERSON>\Nova\Nova;
use App\Models\Submission;
use App\Enums\EndpointEnum;

class CompanyRegistrationSubmittedTrend extends Trend
{
    public $name = 'Company Registrations Submitted';

    /**
     * Calculate the value of the metric.
     *
     * @param \Laravel\Nova\Http\Requests\NovaRequest $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        return $this->countByDays($request, Submission::where('endpoint_id', EndpointEnum::COMPANY_REGISTRATION->value));
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            30 => Nova::__('30 Days'),
            60 => Nova::__('60 Days'),
            90 => Nova::__('90 Days'),
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'company-registration-submitted-trend';
    }
}
