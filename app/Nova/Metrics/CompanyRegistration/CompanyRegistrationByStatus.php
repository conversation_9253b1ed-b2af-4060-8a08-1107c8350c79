<?php

namespace App\Nova\Metrics\CompanyRegistration;

use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Partition;
use App\Enums\EndpointEnum;
use App\Models\Submission;
use App\Enums\SubmissionStatusEnum;

class CompanyRegistrationByStatus extends Partition
{
    public $name = 'Company Registration By Status';

    /**
     * Calculate the value of the metric.
     *
     * @param \Laravel\Nova\Http\Requests\NovaRequest $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $submissions = Submission::selectRaw('count(*) as total,status')
            ->where('endpoint_id', EndpointEnum::COMPANY_REGISTRATION->value)
            ->groupBy('status')
            ->get();
        $results = [];
        $colors = [];
        $submissions->each(function ($item) use (&$results, &$colors) {
            $results[$item->status->value] = $item->total;
            if ($item->status == SubmissionStatusEnum::SUCCESS) {
                $colors[$item->status->value] = 'green';
            } elseif ($item->status == SubmissionStatusEnum::FAILED) {
                $colors[$item->status->value] = 'red';
            } elseif ($item->status == SubmissionStatusEnum::PENDING) {
                $colors[$item->status->value] = 'orange';
            } elseif ($item->status == SubmissionStatusEnum::PROCESSING) {
                $colors[$item->status->value] = 'blue';
            } elseif ($item->status == SubmissionStatusEnum::OTP_PENDING) {
                $colors[$item->status->value] = 'yellow';
            } elseif ($item->status == SubmissionStatusEnum::MANUAL_REVIEW) {
                $colors[$item->status->value] = 'purple';
            } else {
                $colors[$item->status->value] = 'gray';
            }
        });
        return $this->result($results)->colors($colors);
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'company-registration-by-status';
    }
}
