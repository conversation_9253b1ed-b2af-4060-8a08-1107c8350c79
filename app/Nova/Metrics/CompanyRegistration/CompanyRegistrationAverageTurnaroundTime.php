<?php

namespace App\Nova\Metrics\CompanyRegistration;

use App\Enum\CipcTypeEnum;
use App\Models\CipcTransaction;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Value;
use <PERSON>vel\Nova\Nova;
use App\Models\Submission;
use App\Enums\EndpointEnum;
use App\Enums\SubmissionStatusEnum;

class CompanyRegistrationAverageTurnaroundTime extends Value
{
    public $name = 'Average Turnaround Time';

    /**
     * Calculate the value of the metric.
     *
     * @param \Laravel\Nova\Http\Requests\NovaRequest $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        // Get the start and end dates for the current and previous ranges
        $currentRange = $this->currentRange($request->range, $request->timezone);
        $previousRange = $this->previousRange($request->range, $request->timezone);
        // Calculate the average turnaround time for the current range
        $currentAvgTurnaroundTime = (float)Submission::query()
            ->where('endpoint_id', EndpointEnum::COMPANY_REGISTRATION->value)
            ->whereBetween('created_at', [$currentRange[0], $currentRange[1]])
            ->where('status', SubmissionStatusEnum::SUCCESS->value)
            ->selectRaw('AVG(timestampdiff(HOUR,created_at,updated_at)) as avg_turnaround_time')
            ->value('avg_turnaround_time');

        // Calculate the average turnaround time for the previous range
        $previousAvgTurnaroundTime = (float)Submission::query()
            ->where('endpoint_id', EndpointEnum::COMPANY_REGISTRATION->value)
            ->whereBetween('created_at', [$previousRange[0], $previousRange[1]])
            ->where('status', SubmissionStatusEnum::SUCCESS->value)
            ->selectRaw('AVG(timestampdiff(HOUR,created_at, updated_at)) as avg_turnaround_time')
            ->value('avg_turnaround_time');


        // Return the result with the previous value and the percentage change
        return $this->result(round($currentAvgTurnaroundTime, 2))
            ->previous(round($previousAvgTurnaroundTime, 2))
            ->suffix('hours');
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            30 => Nova::__('30 Days'),
            60 => Nova::__('60 Days'),
            365 => Nova::__('365 Days'),
            'TODAY' => Nova::__('Today'),
            'MTD' => Nova::__('Month To Date'),
            'QTD' => Nova::__('Quarter To Date'),
            'YTD' => Nova::__('Year To Date'),
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }
}
