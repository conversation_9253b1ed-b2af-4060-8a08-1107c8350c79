<?php

namespace App\Nova\Metrics;

use App\Models\Submission;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Value;
use Carbon\CarbonImmutable;

class SubmissionSuccessRate extends Value
{
    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        // Get the start and end dates for the current and previous ranges
        $currentRange = $this->currentRange($request->range, $request->timezone);
        $previousRange = $this->previousRange($request->range, $request->timezone);

        // Calculate the success rate for the current range
        $currentSuccessRate = $this->calculateSuccessRate($currentRange[0], $currentRange[1]);

        // Calculate the success rate for the previous range
        $previousSuccessRate = $this->calculateSuccessRate($previousRange[0], $previousRange[1]);

        // Return the result with the previous value
        return $this->result($currentSuccessRate)
            ->previous($previousSuccessRate);
    }

    /**
     * Calculate the success rate for a given date range.
     *
     * @param  \Carbon\CarbonImmutable  $start
     * @param  \Carbon\CarbonImmutable  $end
     * @return float
     */
    protected function calculateSuccessRate(CarbonImmutable $start, CarbonImmutable $end): float
    {
        $totalSubmissions = Submission::query()
            ->whereBetween('created_at', [$start, $end])
            ->count();

        if ($totalSubmissions === 0) {
            return 0;
        }

        $successfulSubmissions = Submission::query()
            ->whereBetween('created_at', [$start, $end])
            ->where('status', 'success')
            ->count();

        return ($successfulSubmissions / $totalSubmissions) * 100;
    }

    /**
     * Get the name of the metric.
     *
     * @return string
     */
    public function name()
    {
        return 'Submission Success Rate';
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            1 => __('Today'),
            7 => __('This Week'),
            30 => __('This Month'),
            60 => __('This Quarter'),
            365 => __('This Year'),
            'ALL' => __('All Time'),
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'submission-success-rate';
    }

    /**
     * Get the current range for the metric.
     *
     * @param  string|int  $range
     * @param  string  $timezone
     * @return array
     */
    protected function currentRange(string|int $range, string $timezone): array
    {
        if ($range === 'ALL') {
            return [
                CarbonImmutable::createFromTimestamp(0, $timezone),
                CarbonImmutable::now($timezone),
            ];
        }

        return parent::currentRange($range, $timezone);
    }

    /**
     * Get the previous range for the metric.
     *
     * @param  string|int  $range
     * @param  string  $timezone
     * @return array
     */
    protected function previousRange(string|int $range, string $timezone): array
    {
        if ($range === 'ALL') {
            return [
                CarbonImmutable::createFromTimestamp(0, $timezone),
                CarbonImmutable::now($timezone),
            ];
        }

        return parent::previousRange($range, $timezone);
    }
}
