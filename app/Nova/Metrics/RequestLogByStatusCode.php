<?php

namespace App\Nova\Metrics;

use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Metrics\Partition;
use App\Models\RequestLog;

class RequestLogByStatusCode extends Partition
{
    public $name = 'Request Logs By Status Code';

    /**
     * Calculate the value of the metric.
     *
     * @param \Laravel\Nova\Http\Requests\NovaRequest $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $requestLogs = RequestLog::selectRaw('count(*) as total, status_code')
            ->groupBy('status_code')
            ->get();

        $results = [];
        $colors = [];

        $requestLogs->each(function ($item) use (&$results, &$colors) {
            $statusCode = $item->status_code ?? 'Unknown';
            $results[$statusCode] = $item->total;

            // Set green color for 200 and 201 status codes, red for others
            if ($statusCode == 200 || $statusCode == 201) {
                $colors[$statusCode] = 'green';
            } else {
                $colors[$statusCode] = 'red';
            }
        });

        return $this->result($results)->colors($colors);
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'request-log-by-status-code';
    }
}
