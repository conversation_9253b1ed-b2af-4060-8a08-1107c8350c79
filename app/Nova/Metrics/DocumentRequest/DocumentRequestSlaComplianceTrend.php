<?php

namespace App\Nova\Metrics\DocumentRequest;

use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Trend;
use <PERSON>vel\Nova\Metrics\TrendResult;
use Laravel\Nova\Nova;
use App\Models\Submission;
use App\Enums\EndpointEnum;
use App\Enums\SubmissionStatusEnum;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DocumentRequestSlaComplianceTrend extends Trend
{
    public $name = 'SLA Compliance Trend';

    /**
     * Calculate the value of the metric.
     *
     * @param \Laravel\Nova\Http\Requests\NovaRequest $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $range = $request->range;
        $startDate = now()->subDays($range);
        $endDate = now();

        $query = Submission::query()
            ->select([
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as total'),
                DB::raw('IF(COUNT(*) > 0,
                    ROUND(
                        (SUM(IF(
                            status = ? AND TIMESTAMPDIFF(SECOND, created_at, updated_at) <= (SELECT turnaround_time_sec FROM endpoints WHERE id = ?),
                            1,
                            0
                        )) * 100.0) / COUNT(*),
                    2),
                    0
                ) as compliance_rate')
            ])
            ->where('endpoint_id', EndpointEnum::DOCUMENT_REQUEST->value)
            ->where('status', SubmissionStatusEnum::SUCCESS->value)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->addBinding([
                SubmissionStatusEnum::SUCCESS->value,
                EndpointEnum::DOCUMENT_REQUEST->value
            ], 'select')
            ->groupBy('date')
            ->orderBy('date');

        $results = $query->get()->keyBy('date');

        // Create an array with all dates in the range
        $trend = [];
        $currentDate = Carbon::parse($startDate);
        $endDateCarbon = Carbon::parse($endDate);

        while ($currentDate <= $endDateCarbon) {
            $dateString = $currentDate->format('Y-m-d');
            $trend[$currentDate->format('F j, Y')] = $results->get($dateString)?->compliance_rate ?? 0;
            $currentDate->addDay();
        }

        return (new TrendResult)
            ->trend($trend)
            ->suffix('%')
            ->showLatestValue();
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            30 => Nova::__('30 Days'),
            60 => Nova::__('60 Days'),
            90 => Nova::__('90 Days'),
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'document-request-sla-compliance-trend';
    }
}
