<?php

namespace App\Nova\Metrics;

use App\Models\Submission;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Value;
use Carbon\CarbonImmutable;

class SubmissionAverageProcessingTime extends Value
{
    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        // Get the start and end dates for the current and previous ranges
        $currentRange = $this->currentRange($request->range, $request->timezone);
        $previousRange = $this->previousRange($request->range, $request->timezone);

        // Calculate the average processing time for the current range
        $currentAverageTime = $this->calculateAverageProcessingTime($currentRange[0], $currentRange[1]);

        // Calculate the average processing time for the previous range
        $previousAverageTime = $this->calculateAverageProcessingTime($previousRange[0], $previousRange[1]);

        // Return the result with the previous value
        return $this->result($currentAverageTime)
            ->previous($previousAverageTime);
    }

    /**
     * Calculate the average processing time for a given date range.
     *
     * @param  \Carbon\CarbonImmutable  $start
     * @param  \Carbon\CarbonImmutable  $end
     * @return float
     */
    protected function calculateAverageProcessingTime(CarbonImmutable $start, CarbonImmutable $end): float
    {
        $submissions = Submission::query()
            ->whereBetween('created_at', [$start, $end])
            ->whereNotNull('processed_at')
            ->get();

        if ($submissions->isEmpty()) {
            return 0;
        }

        $totalProcessingTime = 0;
        $count = 0;

        foreach ($submissions as $submission) {
            $processingTime = $submission->processed_at->diffInSeconds($submission->created_at);
            $totalProcessingTime += $processingTime;
            $count++;
        }

        return $count > 0 ? $totalProcessingTime / $count : 0;
    }

    /**
     * Get the name of the metric.
     *
     * @return string
     */
    public function name()
    {
        return 'Average Processing Time';
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            1 => __('Today'),
            7 => __('This Week'),
            30 => __('This Month'),
            60 => __('This Quarter'),
            365 => __('This Year'),
            'ALL' => __('All Time'),
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'submission-average-processing-time';
    }

    /**
     * Get the current range for the metric.
     *
     * @param  string|int  $range
     * @param  string  $timezone
     * @return array
     */
    protected function currentRange(string|int $range, string $timezone): array
    {
        if ($range === 'ALL') {
            return [
                CarbonImmutable::createFromTimestamp(0, $timezone),
                CarbonImmutable::now($timezone),
            ];
        }

        return parent::currentRange($range, $timezone);
    }

    /**
     * Get the previous range for the metric.
     *
     * @param  string|int  $range
     * @param  string  $timezone
     * @return array
     */
    protected function previousRange(string|int $range, string $timezone): array
    {
        if ($range === 'ALL') {
            return [
                CarbonImmutable::createFromTimestamp(0, $timezone),
                CarbonImmutable::now($timezone),
            ];
        }

        return parent::previousRange($range, $timezone);
    }
}
