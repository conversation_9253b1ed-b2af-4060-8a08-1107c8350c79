<?php

namespace App\Nova\Metrics\NameChange;

use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Table;
use <PERSON>vel\Nova\Metrics\MetricTableRow;
use App\Models\Submission;
use App\Enums\EndpointEnum;
use App\Enums\SubmissionStatusEnum;
use Illuminate\Support\Facades\DB;

class NameChangeSlaCompliance extends Table
{
    public $name = 'SLA Compliance';
    public $width = '2/3';

    /**
     * Calculate the value of the metric.
     *
     * @param \Laravel\Nova\Http\Requests\NovaRequest $request
     * @return array<int, \Laravel\Nova\Metrics\MetricTableRow>
     */
    public function calculate(NovaRequest $request): array
    {
        $query = Submission::query()
            ->select([
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE
                    WHEN status = ? AND TIMESTAMPDIFF(SECOND, created_at, updated_at) <= (SELECT turnaround_time_sec FROM endpoints WHERE id = ?)
                    THEN 1 ELSE 0 END) as within_sla'),
                DB::raw('ROUND(SUM(CASE
                    WHEN status = ? AND TIMESTAMPDIFF(SECOND, created_at, updated_at) <= (SELECT turnaround_time_sec FROM endpoints WHERE id = ?)
                    THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as compliance_rate')
            ])
            ->where('endpoint_id', EndpointEnum::NAME_CHANGE->value)
            ->where('status', SubmissionStatusEnum::SUCCESS->value)
            ->addBinding([
                SubmissionStatusEnum::SUCCESS->value,
                EndpointEnum::NAME_CHANGE->value,
                SubmissionStatusEnum::SUCCESS->value,
                EndpointEnum::NAME_CHANGE->value
            ], 'select');

        // Get data for different time periods
        $periods = [
            'Last 7 Days' => now()->subDays(7),
            'Last 30 Days' => now()->subDays(30),
            'Last 60 Days' => now()->subDays(60),
            'All Time' => null
        ];

        $rows = [];
        foreach ($periods as $period => $startDate) {
            $periodQuery = clone $query;
            if ($startDate) {
                $periodQuery->where('created_at', '>=', $startDate);
            }

            $result = $periodQuery->first();

            $rows[] = MetricTableRow::make()
                ->title($period)
                ->subtitle("Total: {$result->total}, Within SLA: {$result->within_sla}, Compliance Rate: {$result->compliance_rate}%");
        }

        return $rows;
    }

    /**
     * Get the name of the metric.
     *
     * @return string
     */
    public function name()
    {
        return 'SLA Compliance';
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'name-change-sla-compliance';
    }
}
