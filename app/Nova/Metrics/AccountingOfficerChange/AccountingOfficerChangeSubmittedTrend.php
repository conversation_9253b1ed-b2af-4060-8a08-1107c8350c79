<?php

namespace App\Nova\Metrics\AccountingOfficerChange;

use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Trend;
use <PERSON>vel\Nova\Nova;
use App\Models\Submission;
use App\Enums\EndpointEnum;

class AccountingOfficerChangeSubmittedTrend extends Trend
{
    public $name = 'Accounting Officer Changes Submitted';

    /**
     * Calculate the value of the metric.
     *
     * @param \Laravel\Nova\Http\Requests\NovaRequest $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        return $this->countByDays(
            $request,
            Submission::query()->where('endpoint_id', EndpointEnum::ACCOUNTING_OFFICER_CHANGE->value),
            'created_at'
        );
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            30 => Nova::__('30 Days'),
            60 => Nova::__('60 Days'),
            90 => Nova::__('90 Days'),
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'accounting-officer-change-submitted-trend';
    }
}
