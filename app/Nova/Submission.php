<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\Select;
use Laravel\Nova\Fields\Badge;
use App\Enums\SubmissionStatusEnum;
use App\Enums\CustomerTypeEnum;
use App\Nova\Filters\EndpointFilter;
use App\Nova\Filters\StatusFilter;
use App\Nova\Filters\CustomerTypeFilter;
use App\Nova\Filters\DateRangeFilter;
use <PERSON>vel\Nova\Fields\Textarea;

class Submission extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Submission>
     */
    public static $model = \App\Models\Submission::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'reference';

    /**
     * The order of the resource in the navigation menu.
     *
     * @var int
     */
    public static $order = 1;

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'reference',
        'status',
        'customer_type',
        'customer_id',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<int, \Laravel\Nova\Fields\Field|\Laravel\Nova\Panel|\Laravel\Nova\ResourceTool|\Illuminate\Http\Resources\MergeValue>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Account')
                ->sortable()
                ->rules('required'),

            BelongsTo::make('Endpoint')
                ->sortable()
                ->rules('required'),

            Text::make('Reference')
                ->sortable()
                ->rules('required', 'max:255'),

            Select::make('Status')
                ->sortable()
                ->rules('required')
                ->options(array_combine(
                    array_map(fn($case) => $case->value, SubmissionStatusEnum::cases()),
                    array_map(fn($case) => ucfirst($case->value), SubmissionStatusEnum::cases())
                ))
                ->displayUsingLabels()
                ->resolveUsing(function ($value) {
                    if (empty($value)) {
                        return SubmissionStatusEnum::PENDING->value;
                    }

                    if ($value instanceof SubmissionStatusEnum) {
                        return $value->value;
                    }

                    try {
                        return SubmissionStatusEnum::from($value)->value;
                    } catch (\ValueError $e) {
                        return SubmissionStatusEnum::PENDING->value;
                    }
                })
                ->onlyOnForms(),

            Text::make('Status')
                ->resolveUsing(function ($value, $resource) {
                    $status = $resource->status ?? SubmissionStatusEnum::PENDING->value;

                    try {
                        $enum = $status instanceof SubmissionStatusEnum ? $status : SubmissionStatusEnum::from($status);
                    } catch (\ValueError $e) {
                        $enum = SubmissionStatusEnum::PENDING;
                    }

                    $classes = match ($enum) {
                        SubmissionStatusEnum::FAILED => 'bg-red-100 text-red-600',
                        SubmissionStatusEnum::SUCCESS => 'bg-green-100 text-green-600',
                        SubmissionStatusEnum::PROCESSING => 'bg-blue-100 text-blue-600',
                        SubmissionStatusEnum::OTP_PENDING => 'bg-yellow-100 text-yellow-600',
                        default => 'bg-gray-100 text-gray-600',
                    };

                    return "<span class='px-2 py-1 rounded-full text-xs font-medium {$classes}'>{$enum->value}</span>";
                })
                ->asHtml()
                ->exceptOnForms(),

            Select::make('Customer Type')
                ->sortable()
                ->rules('required')
                ->options(array_combine(
                    array_map(fn($case) => $case->value, CustomerTypeEnum::cases()),
                    array_map(fn($case) => ucfirst($case->value), CustomerTypeEnum::cases())
                ))
                ->displayUsingLabels()
                ->resolveUsing(function ($value) {
                    if (empty($value)) {
                        return CustomerTypeEnum::CIPC->value;
                    }

                    if ($value instanceof CustomerTypeEnum) {
                        return $value->value;
                    }

                    try {
                        return CustomerTypeEnum::from($value)->value;
                    } catch (\ValueError $e) {
                        return CustomerTypeEnum::CIPC->value;
                    }
                }),

            Text::make('Customer ID')
                ->sortable()
                ->hideFromIndex(),

            Code::make('json_data')
                ->json()
                ->hideFromIndex(),

            BelongsTo::make('Request Log')
                ->sortable()
                ->onlyOnDetail(),

            DateTime::make('Submitted At')
                ->sortable(),

            HasMany::make('Submission Logs'),

            DateTime::make('Created At')
                ->sortable()
                ->onlyOnDetail(),

            DateTime::make('Updated At')
                ->sortable()
                ->onlyOnDetail(),

            Textarea::make('Manual Review Notes')
                ->sortable()
                ->nullable()
                ->hideFromIndex(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array<int, \Laravel\Nova\Filters\Filter>
     */
    public function filters(NovaRequest $request): array
    {
        return [
            new EndpointFilter,
            new StatusFilter,
            new CustomerTypeFilter,
            new DateRangeFilter,
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array<int, \Laravel\Nova\Lenses\Lens>
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array<int, \Laravel\Nova\Actions\Action>
     */
    public function actions(NovaRequest $request): array
    {
        return [
            new \App\Nova\Actions\ChangeSubmissionStatusAction,
        ];
    }
}
