<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\Code;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class Account extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Account>
     */
    public static $model = \App\Models\Account::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The order of the resource in the navigation menu.
     *
     * @var int
     */
    public static $order = 2;

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
        'email',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255'),

            BelongsTo::make('Subscription Plan'),

            DateTime::make('Subscription End Date')
                ->sortable(),

            Text::make('Webhook URL')
                ->hideFromIndex(),

            Code::make('Webhook URL Headers')
                ->json()
                ->hideFromIndex(),

            HasMany::make('Submissions'),

            HasMany::make('Submission Logs'),

            HasMany::make('Request Logs'),

            HasMany::make('Webhooks'),


            HasMany::make('Cipc Customers'),
            HasMany::make('Users'),
            HasMany::make('Subscriptions'),
        ];
    }

    public function cards(NovaRequest $request): array
    {
        return [];
    }

    public function filters(NovaRequest $request): array
    {
        return [
            new \App\Nova\Filters\DateRangeFilter,
        ];
    }

    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
