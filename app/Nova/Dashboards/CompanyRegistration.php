<?php

namespace App\Nova\Dashboards;

use Lara<PERSON>\Nova\Dashboards\Main as Dashboard;
use Laravel\Nova\Cards\Help;
use Laravel\Nova\Http\Requests\NovaRequest;
use App\Nova\Metrics\CompanyRegistration\CompanyRegistrationByStatus;
use App\Nova\Metrics\CompanyRegistration\CompanyRegistrationAverageTurnaroundTime;
use App\Nova\Metrics\CompanyRegistration\CompanyRegistrationSubmittedTrend;
use App\Nova\Metrics\CompanyRegistration\CompanyRegistrationSlaCompliance;
use App\Nova\Metrics\CompanyRegistration\CompanyRegistrationSlaComplianceTrend;

class CompanyRegistration extends Dashboard
{
    /**
     * Get the displayable name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Company Registration';
    }

    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new CompanyRegistrationByStatus,
            new CompanyRegistrationAverageTurnaroundTime,
            new CompanyRegistrationSubmittedTrend,
            new CompanyRegistrationSlaCompliance,
            new CompanyRegistrationSlaComplianceTrend,
        ];
    }
}
