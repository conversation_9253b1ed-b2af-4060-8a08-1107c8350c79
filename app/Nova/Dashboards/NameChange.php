<?php

namespace App\Nova\Dashboards;

use <PERSON><PERSON>\Nova\Dashboards\Main as Dashboard;
use <PERSON><PERSON>\Nova\Cards\Help;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use App\Nova\Metrics\NameChange\NameChangeByStatus;
use App\Nova\Metrics\NameChange\NameChangeAverageTurnaroundTime;
use App\Nova\Metrics\NameChange\NameChangeSubmittedTrend;
use App\Nova\Metrics\NameChange\NameChangeSlaCompliance;
use App\Nova\Metrics\NameChange\NameChangeSlaComplianceTrend;

class NameChange extends Dashboard
{
    /**
     * Get the displayable name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Name Change';
    }

    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new NameChangeByStatus,
            new NameChangeAverageTurnaroundTime,
            new NameChangeSubmittedTrend,
            new NameChangeSlaCompliance,
            new NameChangeSlaComplianceTrend,
        ];
    }
}
