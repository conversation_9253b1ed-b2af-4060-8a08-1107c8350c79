<?php

namespace App\Nova\Dashboards;

use <PERSON><PERSON>\Nova\Cards\Help;
use <PERSON><PERSON>\Nova\Dashboards\Main as Dashboard;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use App\Nova\Metrics\AuthorisedShareChange\AuthorisedShareChangeByStatus;
use App\Nova\Metrics\AuthorisedShareChange\AuthorisedShareChangeAverageTurnaroundTime;
use App\Nova\Metrics\AuthorisedShareChange\AuthorisedShareChangeSubmittedTrend;
use App\Nova\Metrics\AuthorisedShareChange\AuthorisedShareChangeSlaCompliance;
use App\Nova\Metrics\AuthorisedShareChange\AuthorisedShareChangeSlaComplianceTrend;

class AuthorisedShareChange extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new AuthorisedShareChangeByStatus,
            new AuthorisedShareChangeAverageTurnaroundTime,
            new AuthorisedShareChangeSubmittedTrend,
            new AuthorisedShareChangeSlaCompliance,
            new AuthorisedShareChangeSlaComplianceTrend,
        ];
    }

    /**
     * Get the name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Authorised Share Change';
    }
}
