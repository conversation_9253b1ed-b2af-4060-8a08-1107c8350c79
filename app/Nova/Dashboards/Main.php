<?php

namespace App\Nova\Dashboards;

use App\Nova\Metrics\AccountCount;
use App\Nova\Metrics\SubmissionCount;
use App\Nova\Metrics\RequestLogCount;
use App\Nova\Metrics\WebhookCount;
use App\Nova\Metrics\UserCount;
use App\Nova\Metrics\RequestLogByStatusCode;
use App\Nova\Metrics\ValidationFailuresTrend;
use Laravel\Nova\Dashboards\Main as Dashboard;

class Main extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new AccountCount,
            new SubmissionCount,
            new RequestLogCount,
            new WebhookCount,
            new UserCount,
            new RequestLogByStatusCode,
            new ValidationFailuresTrend,
        ];
    }
}
