<?php

namespace App\Nova\Dashboards;

use Lara<PERSON>\Nova\Dashboards\Main as Dashboard;
use Lara<PERSON>\Nova\Cards\Help;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use App\Nova\Metrics\AccountingOfficerChange\AccountingOfficerChangeByStatus;
use App\Nova\Metrics\AccountingOfficerChange\AccountingOfficerChangeAverageTurnaroundTime;
use App\Nova\Metrics\AccountingOfficerChange\AccountingOfficerChangeSubmittedTrend;
use App\Nova\Metrics\AccountingOfficerChange\AccountingOfficerChangeSlaCompliance;
use App\Nova\Metrics\AccountingOfficerChange\AccountingOfficerChangeSlaComplianceTrend;

class AccountingOfficerChange extends Dashboard
{
    /**
     * Get the displayable name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Accounting Officer Change';
    }

    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new AccountingOfficerChangeByStatus,
            new AccountingOfficerChangeAverageTurnaroundTime,
            new AccountingOfficerChangeSubmittedTrend,
            new AccountingOfficerChangeSlaCompliance,
            new AccountingOfficerChangeSlaComplianceTrend,
        ];
    }
}
