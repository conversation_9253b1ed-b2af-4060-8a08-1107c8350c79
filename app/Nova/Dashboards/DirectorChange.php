<?php

namespace App\Nova\Dashboards;

use <PERSON><PERSON>\Nova\Cards\Help;
use <PERSON>vel\Nova\Dashboards\Main as Dashboard;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use App\Nova\Metrics\DirectorChange\DirectorChangeByStatus;
use App\Nova\Metrics\DirectorChange\DirectorChangeAverageTurnaroundTime;
use App\Nova\Metrics\DirectorChange\DirectorChangeSubmittedTrend;
use App\Nova\Metrics\DirectorChange\DirectorChangeSlaCompliance;
use App\Nova\Metrics\DirectorChange\DirectorChangeSlaComplianceTrend;

class DirectorChange extends Dashboard
{
    /**
     * Get the displayable name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Director Change';
    }

    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new DirectorChangeByStatus,
            new DirectorChangeAverageTurnaroundTime,
            new DirectorChangeSubmittedTrend,
            new DirectorChangeSlaCompliance,
            new DirectorChangeSlaComplianceTrend,
        ];
    }
}
