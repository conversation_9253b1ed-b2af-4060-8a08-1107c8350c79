<?php

namespace App\Nova\Dashboards;

use <PERSON><PERSON>\Nova\Cards\Help;
use <PERSON>vel\Nova\Dashboards\Main as Dashboard;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use App\Nova\Metrics\AuditorChange\AuditorChangeByStatus;
use App\Nova\Metrics\AuditorChange\AuditorChangeAverageTurnaroundTime;
use App\Nova\Metrics\AuditorChange\AuditorChangeSubmittedTrend;
use App\Nova\Metrics\AuditorChange\AuditorChangeSlaCompliance;
use App\Nova\Metrics\AuditorChange\AuditorChangeSlaComplianceTrend;

class AuditorChange extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new AuditorChangeByStatus,
            new AuditorChangeAverageTurnaroundTime,
            new AuditorChangeSubmittedTrend,
            new AuditorChangeSlaCompliance,
            new AuditorChangeSlaComplianceTrend,
        ];
    }

    /**
     * Get the name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Auditor Change';
    }
}
