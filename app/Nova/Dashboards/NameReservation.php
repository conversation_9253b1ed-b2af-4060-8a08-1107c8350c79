<?php

namespace App\Nova\Dashboards;

use Lara<PERSON>\Nova\Dashboards\Main as Dashboard;
use App\Nova\Metrics\NameReservation\NameReservationByStatus;
use App\Nova\Metrics\NameReservation\NameReservationAverageTurnaroundTime;
use App\Nova\Metrics\NameReservation\NameReservationSubmittedTrend;
use App\Nova\Metrics\NameReservation\NameReservationSlaCompliance;
use App\Nova\Metrics\NameReservation\NameReservationSlaComplianceTrend;

class NameReservation extends Dashboard
{
    /**
     * Get the displayable name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Name Reservation';
    }

    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new NameReservationByStatus,
            new NameReservationAverageTurnaroundTime,
            new NameReservationSubmittedTrend,
            new NameReservationSlaCompliance,
            new NameReservationSlaComplianceTrend,
        ];
    }
}
