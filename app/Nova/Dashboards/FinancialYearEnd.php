<?php

namespace App\Nova\Dashboards;

use Laravel\Nova\Dashboards\Main as Dashboard;
use App\Nova\Metrics\FinancialYearEnd\FinancialYearEndByStatus;
use App\Nova\Metrics\FinancialYearEnd\FinancialYearEndAverageTurnaroundTime;
use App\Nova\Metrics\FinancialYearEnd\FinancialYearEndSubmittedTrend;
use App\Nova\Metrics\FinancialYearEnd\FinancialYearEndSlaCompliance;
use App\Nova\Metrics\FinancialYearEnd\FinancialYearEndSlaComplianceTrend;

class FinancialYearEnd extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new FinancialYearEndByStatus,
            new FinancialYearEndAverageTurnaroundTime,
            new FinancialYearEndSubmittedTrend,
            new FinancialYearEndSlaCompliance,
            new FinancialYearEndSlaComplianceTrend,
        ];
    }

    /**
     * Get the name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Financial Year End';
    }
}
