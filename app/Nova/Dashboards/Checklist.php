<?php

namespace App\Nova\Dashboards;

use <PERSON><PERSON>\Nova\Cards\Help;
use <PERSON>vel\Nova\Dashboards\Main as Dashboard;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use App\Nova\Metrics\Checklist\ChecklistByStatus;
use App\Nova\Metrics\Checklist\ChecklistAverageTurnaroundTime;
use App\Nova\Metrics\Checklist\ChecklistSubmittedTrend;
use App\Nova\Metrics\Checklist\ChecklistSlaCompliance;
use App\Nova\Metrics\Checklist\ChecklistSlaComplianceTrend;

class Checklist extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new ChecklistByStatus,
            new ChecklistAverageTurnaroundTime,
            new ChecklistSubmittedTrend,
            new ChecklistSlaCompliance,
            new ChecklistSlaComplianceTrend,
        ];
    }

    /**
     * Get the name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Checklist';
    }
}
