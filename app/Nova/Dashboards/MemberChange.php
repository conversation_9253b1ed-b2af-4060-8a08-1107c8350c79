<?php

namespace App\Nova\Dashboards;

use <PERSON><PERSON>\Nova\Cards\Help;
use <PERSON><PERSON>\Nova\Dashboards\Main as Dashboard;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use App\Nova\Metrics\MemberChange\MemberChangeByStatus;
use App\Nova\Metrics\MemberChange\MemberChangeAverageTurnaroundTime;
use App\Nova\Metrics\MemberChange\MemberChangeSubmittedTrend;
use App\Nova\Metrics\MemberChange\MemberChangeSlaCompliance;
use App\Nova\Metrics\MemberChange\MemberChangeSlaComplianceTrend;

class MemberChange extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new MemberChangeByStatus,
            new MemberChangeAverageTurnaroundTime,
            new MemberChangeSubmittedTrend,
            new MemberChangeSlaCompliance,
            new MemberChangeSlaComplianceTrend,
        ];
    }

    /**
     * Get the name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Member Change';
    }
}
