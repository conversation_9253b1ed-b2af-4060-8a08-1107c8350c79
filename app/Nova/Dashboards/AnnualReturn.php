<?php

namespace App\Nova\Dashboards;

use <PERSON><PERSON>\Nova\Cards\Help;
use <PERSON>vel\Nova\Dashboards\Main as Dashboard;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use App\Nova\Metrics\AnnualReturn\AnnualReturnByStatus;
use App\Nova\Metrics\AnnualReturn\AnnualReturnAverageTurnaroundTime;
use App\Nova\Metrics\AnnualReturn\AnnualReturnSubmittedTrend;
use App\Nova\Metrics\AnnualReturn\AnnualReturnSlaCompliance;
use App\Nova\Metrics\AnnualReturn\AnnualReturnSlaComplianceTrend;

class AnnualReturn extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new AnnualReturnByStatus,
            new AnnualReturnAverageTurnaroundTime,
            new AnnualReturnSubmittedTrend,
            new AnnualReturnSlaCompliance,
            new AnnualReturnSlaComplianceTrend,
        ];
    }

    /**
     * Get the name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Annual Return';
    }
}
