<?php

namespace App\Nova\Dashboards;

use <PERSON><PERSON>\Nova\Cards\Help;
use <PERSON>vel\Nova\Dashboards\Main as Dashboard;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use App\Nova\Metrics\CompanySecretaryChange\CompanySecretaryChangeByStatus;
use App\Nova\Metrics\CompanySecretaryChange\CompanySecretaryChangeAverageTurnaroundTime;
use App\Nova\Metrics\CompanySecretaryChange\CompanySecretaryChangeSubmittedTrend;
use App\Nova\Metrics\CompanySecretaryChange\CompanySecretaryChangeSlaCompliance;
use App\Nova\Metrics\CompanySecretaryChange\CompanySecretaryChangeSlaComplianceTrend;

class CompanySecretaryChange extends Dashboard
{
    /**
     * Get the displayable name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Company Secretary Change';
    }

    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new CompanySecretaryChangeByStatus,
            new CompanySecretaryChangeAverageTurnaroundTime,
            new CompanySecretaryChangeSubmittedTrend,
            new CompanySecretaryChangeSlaCompliance,
            new CompanySecretaryChangeSlaComplianceTrend,
        ];
    }
}
