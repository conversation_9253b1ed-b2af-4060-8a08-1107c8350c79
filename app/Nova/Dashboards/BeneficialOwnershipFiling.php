<?php

namespace App\Nova\Dashboards;

use <PERSON><PERSON>\Nova\Cards\Help;
use <PERSON><PERSON>\Nova\Dashboards\Main as Dashboard;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use App\Nova\Metrics\BeneficialOwnershipFiling\BeneficialOwnershipFilingByStatus;
use App\Nova\Metrics\BeneficialOwnershipFiling\BeneficialOwnershipFilingAverageTurnaroundTime;
use App\Nova\Metrics\BeneficialOwnershipFiling\BeneficialOwnershipFilingSubmittedTrend;
use App\Nova\Metrics\BeneficialOwnershipFiling\BeneficialOwnershipFilingSlaCompliance;
use App\Nova\Metrics\BeneficialOwnershipFiling\BeneficialOwnershipFilingSlaComplianceTrend;

class BeneficialOwnershipFiling extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new BeneficialOwnershipFilingByStatus,
            new BeneficialOwnershipFilingAverageTurnaroundTime,
            new BeneficialOwnershipFilingSubmittedTrend,
            new BeneficialOwnershipFilingSlaCompliance,
            new BeneficialOwnershipFilingSlaComplianceTrend,
        ];
    }

    /**
     * Get the name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'BO Filing';
    }
}
