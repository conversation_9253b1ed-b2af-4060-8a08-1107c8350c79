<?php

namespace App\Nova\Dashboards;

use <PERSON><PERSON>\Nova\Cards\Help;
use <PERSON><PERSON>\Nova\Dashboards\Main as Dashboard;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use App\Nova\Metrics\AddressChange\AddressChangeByStatus;
use App\Nova\Metrics\AddressChange\AddressChangeAverageTurnaroundTime;
use App\Nova\Metrics\AddressChange\AddressChangeSubmittedTrend;
use App\Nova\Metrics\AddressChange\AddressChangeSlaCompliance;
use App\Nova\Metrics\AddressChange\AddressChangeSlaComplianceTrend;

class AddressChange extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new AddressChangeByStatus,
            new AddressChangeAverageTurnaroundTime,
            new AddressChangeSubmittedTrend,
            new AddressChangeSlaCompliance,
            new AddressChangeSlaComplianceTrend,
        ];
    }

    /**
     * Get the name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Address Change';
    }
}
