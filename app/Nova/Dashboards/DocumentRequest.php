<?php

namespace App\Nova\Dashboards;

use <PERSON><PERSON>\Nova\Cards\Help;
use <PERSON>vel\Nova\Dashboards\Main as Dashboard;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use App\Nova\Metrics\DocumentRequest\DocumentRequestByStatus;
use App\Nova\Metrics\DocumentRequest\DocumentRequestAverageTurnaroundTime;
use App\Nova\Metrics\DocumentRequest\DocumentRequestSubmittedTrend;
use App\Nova\Metrics\DocumentRequest\DocumentRequestSlaCompliance;
use App\Nova\Metrics\DocumentRequest\DocumentRequestSlaComplianceTrend;

class DocumentRequest extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(): array
    {
        return [
            new DocumentRequestByStatus,
            new DocumentRequestAverageTurnaroundTime,
            new DocumentRequestSubmittedTrend,
            new DocumentRequestSlaCompliance,
            new DocumentRequestSlaComplianceTrend,
        ];
    }

    /**
     * Get the name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Document Request';
    }
}
