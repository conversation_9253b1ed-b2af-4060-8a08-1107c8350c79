<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\Code;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class Webhook extends Resource
{
    public static $model = \App\Models\Webhook::class;

    public static $title = 'id';

    public static $search = [
        'id',
        'url',
        'status_code',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Account', function () {
                return $this->submission?->account?->name ?? 'N/A';
            })->exceptOnForms(),

            BelongsTo::make('Submission'),

            Text::make('URL')
                ->sortable()
                ->rules('required', 'url'),

            Number::make('Status Code')
                ->sortable()
                ->displayUsing(function ($value) {
                    if ($value === null) {
                        return 'Pending';
                    }

                    $color = match (true) {
                        $value >= 200 && $value < 300 => 'text-green-500',
                        $value >= 300 && $value < 400 => 'text-blue-500',
                        $value >= 400 && $value < 500 => 'text-yellow-500',
                        $value >= 500 => 'text-red-500',
                        default => 'text-gray-500',
                    };

                    return "<span class='{$color}'>{$value}</span>";
                })
                ->asHtml(),

            Number::make('Retry Count')
                ->sortable(),

            DateTime::make('Last Attempt At')
                ->sortable(),

            Code::make('Payload')
                ->json()
                ->hideFromIndex(),

            DateTime::make('Created At')
                ->sortable()
                ->exceptOnForms(),
        ];
    }

    public function cards(NovaRequest $request): array
    {
        return [];
    }

    public function filters(NovaRequest $request): array
    {
        return [
            new \App\Nova\Filters\StatusCodeFilter,
            new \App\Nova\Filters\DateRangeFilter,
        ];
    }

    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
