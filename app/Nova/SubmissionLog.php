<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\Badge;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\HasOneThrough;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use App\Enums\SubmissionStatusEnum;
use App\Enums\ResponseStatusEnum;

class SubmissionLog extends Resource
{
    public static $model = \App\Models\SubmissionLog::class;
    /**
     * The order of the resource in the navigation menu.
     *
     * @var int
     */
    public static $order = 3;

    public static $title = 'id';

    public static $search = [
        'id',
        'function_name',
        'status',
        'status_code',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Submission'),

            Text::make('Endpoint', function ($model) {
                return $model->endpoint?->name ?? 'N/A';
            })->exceptOnForms(),

            Text::make('URL')
                ->hideFromIndex(),

            Text::make('Function Name')
                ->sortable()
                ->rules('required'),

            Select::make('Status')
                ->options(array_combine(
                    array_map(fn($case) => $case->value, SubmissionStatusEnum::cases()),
                    array_map(fn($case) => ucfirst($case->value), SubmissionStatusEnum::cases())
                ))
                ->sortable()
                ->rules('required')
                ->onlyOnForms(),

            Badge::make('Status')
                ->map([
                    SubmissionStatusEnum::PENDING->value => 'warning',
                    SubmissionStatusEnum::FAILED->value => 'danger',
                    SubmissionStatusEnum::PROCESSING->value => 'info',
                    SubmissionStatusEnum::OTP_PENDING->value => 'warning',
                    SubmissionStatusEnum::SUCCESS->value => 'success',
                ])
                ->exceptOnForms(),

            Code::make('Payload')
                ->json()
                ->hideFromIndex(),

            Number::make('Status Code')
                ->sortable()
                ->resolveUsing(function ($value, $resource) {
                    if ($value instanceof ResponseStatusEnum) {
                        return $value->value;
                    }
                    return $value;
                })
                ->displayUsing(function ($value, $resource) {
                    if ($value instanceof ResponseStatusEnum) {
                        $value = $value->value;
                    }

                    $color = match (true) {
                        $value == 200 || $value == 201 => 'text-green-600',
                        default => 'text-red-600',
                    };

                    return "<span class='{$color}'>{$value}</span>";
                })
                ->asHtml(),

            Number::make('Duration', 'duration_ms')
                ->sortable()
                ->displayUsing(function ($value) {
                    if (!$value) return 'N/A';

                    $seconds = round($value / 1000, 2);
                    return "{$value}ms ({$seconds}s)";
                }),

            Code::make('Response Body')
                ->json()
                ->hideFromIndex(),

            DateTime::make('Created At')
                ->sortable()
                ->exceptOnForms(),
        ];
    }

    public function cards(NovaRequest $request): array
    {
        return [];
    }

    public function filters(NovaRequest $request): array
    {
        return [
            new \App\Nova\Filters\StatusFilter,
            new \App\Nova\Filters\StatusCodeFilter,
            new \App\Nova\Filters\DateRangeFilter,
        ];
    }

    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
