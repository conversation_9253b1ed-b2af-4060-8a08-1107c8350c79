<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Fields\Boolean;

class Endpoint extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Endpoint>
     */
    public static $model = \App\Models\Endpoint::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * Indicates if the resource should be globally searchable.
     *
     * @var bool
     */
    public static $globallySearchable = false;

    /**
     * Indicates if the resource should be displayed in the navigation menu.
     *
     * @var bool
     */
    public static $displayInNavigation = true;

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
        'description',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<int, \Laravel\Nova\Fields\Field|\Laravel\Nova\Panel|\Laravel\Nova\ResourceTool|\Illuminate\Http\Resources\MergeValue>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255'),

            //turn around time
            Text::make('Turn Around Time', 'turnaround_time_sec')
                ->sortable(),

            Boolean::make('Enabled')
                ->sortable()
                ->displayUsing(function ($value) {
                    return $value ?
                        '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Yes</span>' :
                        '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">No</span>';
                }),

            Boolean::make('Paid')
                ->sortable()
                ->displayUsing(function ($value) {
                    return $value ?
                        '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Yes</span>' :
                        '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">No</span>';
                }),

            Text::make('Description')
                ->hideFromIndex(),

            DateTime::make('Created At')
                ->sortable()
                ->onlyOnDetail(),

            DateTime::make('Updated At')
                ->sortable()
                ->onlyOnDetail(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array<int, \Laravel\Nova\Filters\Filter>
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array<int, \Laravel\Nova\Lenses\Lens>
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array<int, \Laravel\Nova\Actions\Action>
     */
    public function actions(NovaRequest $request): array
    {
        return [
            new \App\Nova\Actions\ToggleEndpointStatus,
        ];
    }
}
