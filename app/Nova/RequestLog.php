<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\Code;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\HasOne;
use Laravel\Nova\Fields\Badge;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Http\Requests\NovaRequest;
use App\Nova\Filters\EndpointFilter;
use App\Nova\Filters\StatusCodeFilter;
use App\Nova\Filters\DateRangeFilter;

class RequestLog extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\RequestLog>
     */
    public static $model = \App\Models\RequestLog::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'reference';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'reference',
        'status_code',
        'ip_address',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<int, \Laravel\Nova\Fields\Field|\Laravel\Nova\Panel|\Laravel\Nova\ResourceTool|\Illuminate\Http\Resources\MergeValue>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Account')
                ->sortable()
                ->rules('required'),

            BelongsTo::make('Endpoint')
                ->sortable()
                ->rules('required'),

            Text::make('Reference')
                ->sortable()
                ->rules('required', 'max:255'),
            Number::make('Status Code')
                ->sortable()
                ->displayUsing(function ($value) {
                    $color = match (true) {
                        $value == 200 || $value == 201 => 'text-green-600',
                        default => 'text-red-600',
                    };

                    return "<span class='{$color}'>{$value}</span>";
                })
                ->asHtml(),
            Text::make('IP Address')
                ->sortable(),
            Text::make('Personal Access Token Id')
                ->sortable()
                ->hideFromIndex(),

            Code::make('Request Body')
                ->json()
                ->hideFromIndex(),

            Code::make('Response Body')
                ->json()
                ->hideFromIndex(),

            Boolean::make('Is Test')
                ->sortable()
                ->hideFromIndex(),

            Text::make('Tracking No')
                ->sortable()
                ->hideFromIndex(),


            HasOne::make('Submission'),

            DateTime::make('Created At')
                ->sortable()
                ->exceptOnForms(),

            DateTime::make('Updated At')
                ->sortable()
                ->onlyOnDetail(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array<int, \Laravel\Nova\Filters\Filter>
     */
    public function filters(NovaRequest $request): array
    {
        return [
            new \App\Nova\Filters\EndpointFilter,
            new \App\Nova\Filters\StatusCodeFilter,
            new \App\Nova\Filters\DateRangeFilter,
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array<int, \Laravel\Nova\Lenses\Lens>
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array<int, \Laravel\Nova\Actions\Action>
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
