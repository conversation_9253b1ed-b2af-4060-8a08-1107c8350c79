<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class CipcCustomer extends Resource
{
    public static $model = \App\Models\CipcCustomer::class;
    /**
     * Indicates if the resource should be globally searchable.
     *
     * @var bool
     */
    public static $globallySearchable = false;

    /**
     * Indicates if the resource should be displayed in the navigation menu.
     *
     * @var bool
     */
    public static $displayInNavigation = false;

    public static $title = 'code';

    public static $search = [
        'id',
        'code',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Account'),

            Text::make('Code')
                ->sortable()
                ->rules('required'),

            Text::make('Password')
                ->sortable()
                ->rules('required')
                ->hideFromIndex(),

            Number::make('Balance')
                ->sortable()
                ->step(0.01)
                ->rules('required', 'numeric', 'min:0'),
        ];
    }

    public function cards(NovaRequest $request): array
    {
        return [];
    }

    public function filters(NovaRequest $request): array
    {
        return [];
    }

    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
