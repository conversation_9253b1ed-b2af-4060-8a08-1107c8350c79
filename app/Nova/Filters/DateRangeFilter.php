<?php

namespace App\Nova\Filters;

use <PERSON><PERSON>\Nova\Filters\DateFilter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Nova;

class DateRangeFilter extends DateFilter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        $now = now();

        switch ($value) {
            case 1:
                return $query->where('created_at', '>=', $now->subDay());
            case 30:
                return $query->where('created_at', '>=', $now->subDays(30));
            case 365:
                return $query->where('created_at', '>=', $now->subDays(365));
            case 'TODAY':
                return $query->where('created_at', '>=', $now->toDateString());
            case 'MTD':
                return $query->where('created_at', '>=', $now->startOfMonth());
            case 'YTD':
                return $query->where('created_at', '>=', $now->startOfYear());
            default:
                return $query;
        }
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function options(NovaRequest $request)
    {
        return [
            'Last Day' => 1,
            '30 Days' => 30,
            '365 Days' => 365,
            'Today' => 'TODAY',
            'Month To Date' => 'MTD',
            'Year To Date' => 'YTD',
        ];
    }
}
