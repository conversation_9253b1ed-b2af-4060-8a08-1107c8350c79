<?php

namespace App\Nova\Filters;

use <PERSON><PERSON>\Nova\Filters\Filter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use App\Enums\CustomerTypeEnum;

class CustomerTypeFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('customer_type', $value);
    }

    /**
     * Get the filter's available options.
     *
     * @return array
     */
    public function options(NovaRequest $request)
    {
        $options = [];
        foreach (CustomerTypeEnum::cases() as $case) {
            $options[ucfirst($case->value)] = $case->value;
        }
        return $options;
    }

    /**
     * Get the displayable name of the filter.
     *
     * @return string
     */
    public function name()
    {
        return 'Customer Type';
    }
}
