<?php

namespace App\Nova\Actions;

use App\Enums\SubmissionStatusEnum;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Actions\ActionResponse;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class ChangeSubmissionStatusAction extends Action
{
    use InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Get the displayable name of the action.
     *
     * @return string
     */
    public function name()
    {
        return 'Change Status';
    }

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return \Laravel\Nova\Actions\ActionResponse
     */
    public function handle(ActionFields $fields, Collection $models): ActionResponse
    {
        $count = 0;
        $newStatus = $fields->status;

        foreach ($models as $submission) {
            $submission->update([
                'status' => $newStatus,
            ]);

            $count++;
        }

        return ActionResponse::message("Successfully updated status for {$count} submissions.");
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request): array
    {
        return [
            Select::make('Status')
                ->options(array_combine(
                    array_map(fn($case) => $case->value, SubmissionStatusEnum::cases()),
                    array_map(fn($case) => ucfirst($case->value), SubmissionStatusEnum::cases())
                ))
                ->displayUsingLabels()
                ->rules('required'),
        ];
    }
}
