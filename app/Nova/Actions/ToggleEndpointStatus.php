<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ToggleEndpointStatus extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $status = (bool) $fields->status;

        $models->each(function ($model) use ($status) {
            $model->update(['enabled' => $status]);
        });

        return Action::message('Endpoints have been ' . ($status ? 'enabled' : 'disabled') . ' successfully.');
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Status')
                ->options([
                    1 => 'Enable',
                    0 => 'Disable',
                ])
                ->rules('required'),
        ];
    }
}
