<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class Subscription extends Resource
{
    public static $model = \App\Models\Subscription::class;
    /**
     * Indicates if the resource should be globally searchable.
     *
     * @var bool
     */
    public static $globallySearchable = false;

    /**
     * Indicates if the resource should be displayed in the navigation menu.
     *
     * @var bool
     */
    public static $displayInNavigation = false;

    public static $title = 'id';

    public static $search = [
        'id',
        'account_id',
        'subscription_plan_id',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Account'),

            BelongsTo::make('Subscription Plan'),

            Date::make('Start Date')
                ->sortable()
                ->rules('required'),

            Date::make('End Date')
                ->sortable(),

            Boolean::make('Active')
                ->sortable(),

            Number::make('Rate Usage')
                ->sortable()
                ->rules('required', 'integer', 'min:0'),
        ];
    }

    public function cards(NovaRequest $request): array
    {
        return [];
    }

    public function filters(NovaRequest $request): array
    {
        return [];
    }

    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
