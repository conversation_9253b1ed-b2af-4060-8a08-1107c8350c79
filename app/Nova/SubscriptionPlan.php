<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class SubscriptionPlan extends Resource
{
    public static $model = \App\Models\SubscriptionPlan::class;
    /**
     * Indicates if the resource should be globally searchable.
     *
     * @var bool
     */
    public static $globallySearchable = false;

    /**
     * Indicates if the resource should be displayed in the navigation menu.
     *
     * @var bool
     */
    public static $displayInNavigation = false;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
        'description',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255'),

            Textarea::make('Description')
                ->hideFromIndex(),

            Number::make('Price')
                ->sortable()
                ->step(0.01)
                ->rules('required', 'numeric', 'min:0'),

            Number::make('Rate Limit')
                ->sortable()
                ->rules('required', 'integer', 'min:0'),

            HasMany::make('Subscriptions'),

            HasMany::make('Accounts'),
        ];
    }

    public function cards(NovaRequest $request): array
    {
        return [];
    }

    public function filters(NovaRequest $request): array
    {
        return [];
    }

    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
