<?php

namespace App\Providers;

use App\Models\PersonalAccessToken;
use Aws\Lambda\LambdaClient;
use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Scribe;
use <PERSON>nuckles\Camel\Extraction\ExtractedEndpointData;
use Symfony\Component\HttpFoundation\Request;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(LambdaClient::class, function ($app) {
            return new LambdaClient([
                'version' => 'latest',
                'region' => config('services.aws.region'),
                'credentials' => [
                    'key' => config('services.aws.key'),
                    'secret' => config('services.aws.secret'),
                ],
            ]);
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (class_exists(Scribe::class)) {
            Scribe::beforeResponseCall(function (Request $request, ExtractedEndpointData $endpointData) {
                if (empty($request->headers->get('Authorization'))) {
                    $token = PersonalAccessToken::where('account_id', $request->user()->account_id)->latest()->first()->token ?? null;
                    if ($token) {
                        $request->headers->add(["Authorization" => "Bearer $token"]);
                    }
                }
                $request->query->add(["is_test" => 1]);
            });
        }
    }
}
