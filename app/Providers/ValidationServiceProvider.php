<?php

namespace App\Providers;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;

class ValidationServiceProvider extends ServiceProvider
{
    public function boot()
    {
        Validator::extendImplicit('required_if_all', function ($attribute, $value, $parameters, $validator) {
            $data = $validator->getData();
            $conditionsMet = true;

            for ($i = 0; $i < count($parameters); $i += 2) {
                $field = $parameters[$i];
                $expectedValue = $parameters[$i + 1];

                $passedValue = data_get($data, $field);

                // Normalize values
                $normalizedPassedValue = filter_var($passedValue, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
                $normalizedExpectedValue = filter_var($expectedValue, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);

                // If normalization returns null, fall back to original values for comparison
                if (is_null($normalizedPassedValue)) {
                    $normalizedPassedValue = $passedValue;
                }
                if (is_null($normalizedExpectedValue)) {
                    $normalizedExpectedValue = $expectedValue;
                }

                if ($normalizedPassedValue != $normalizedExpectedValue) {
                    $conditionsMet = false;
                    break;
                }
            }

            if ($conditionsMet) {
                $attributeValue = data_get($data, $attribute);

                return !is_null($attributeValue) && $attributeValue !== '';
            }

            return true;
        });
    }

    public function register()
    {
        //
    }
}
