<?php

namespace App\Providers;

use App\Enums\RoleEnum;
use App\Models\User;
use Illuminate\Support\Facades\Gate;
use Laravel\Fortify\Features;
use Laravel\Nova\Nova;
use Laravel\Nova\NovaApplicationServiceProvider;
use Illuminate\Support\Facades\Blade;
use Laravel\Nova\Menu\MenuItem;
use Laravel\Nova\Menu\MenuSection;
use Laravel\Nova\Dashboards\Main;
use Illuminate\Http\Request;
use App\Nova\Account;
use App\Nova\Endpoint;
use App\Nova\RequestLog;
use App\Nova\Submission;
use App\Nova\SubmissionLog;
use App\Nova\User as NovaUser;
use App\Nova\Webhook;
use App\Nova\Dashboards\CompanyRegistration;
use App\Nova\Dashboards\AnnualReturn;
use App\Nova\Dashboards\NameReservation;
use App\Nova\Dashboards\MemberChange;
use App\Nova\Dashboards\DirectorChange;
use App\Nova\Dashboards\NameChange;
use App\Nova\Dashboards\AddressChange;
use App\Nova\Dashboards\FinancialYearEnd;
use App\Nova\Dashboards\DocumentRequest;
use App\Nova\Dashboards\AuditorChange;
use App\Nova\Dashboards\AccountingOfficerChange;
use App\Nova\Dashboards\CompanySecretaryChange;
use App\Nova\Dashboards\AuthorisedShareChange;
use App\Nova\Dashboards\Checklist;
use App\Nova\Dashboards\BeneficialOwnershipFiling;

class NovaServiceProvider extends NovaApplicationServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        parent::boot();

        Nova::footer(function ($request) {
            return Blade::render('nova/footer');
        });
        Nova::mainMenu(function (Request $request) {
            return [
                MenuSection::make('Dashboards', [
                    MenuItem::dashboard(Main::class),
                    MenuItem::dashboard(CompanyRegistration::class),
                    MenuItem::dashboard(AnnualReturn::class),
                    MenuItem::dashboard(NameReservation::class),
                    MenuItem::dashboard(MemberChange::class),
                    MenuItem::dashboard(DirectorChange::class),
                    MenuItem::dashboard(NameChange::class),
                    MenuItem::dashboard(AddressChange::class),
                    MenuItem::dashboard(FinancialYearEnd::class),
                    MenuItem::dashboard(DocumentRequest::class),
                    MenuItem::dashboard(AuditorChange::class),
                    MenuItem::dashboard(AccountingOfficerChange::class),
                    MenuItem::dashboard(CompanySecretaryChange::class),
                    MenuItem::dashboard(AuthorisedShareChange::class),
                    MenuItem::dashboard(Checklist::class),
                    MenuItem::dashboard(BeneficialOwnershipFiling::class),


                ])->icon('view-grid')
                    ->collapsable(),
                MenuSection::make('Resources', [
                    MenuItem::resource(Account::class),
                    MenuItem::resource(Submission::class),
                    MenuItem::resource(SubmissionLog::class),
                    MenuItem::resource(RequestLog::class),
                    MenuItem::resource(Webhook::class),
                    MenuItem::resource(NovaUser::class),
                    MenuItem::resource(Endpoint::class),
                    MenuItem::externalLink('Horizon', route('horizon.index'))->openInNewTab(),
                ])->icon('collection')
                    ->collapsable(),
            ];
        });
    }

    /**
     * Register the configurations for Laravel Fortify.
     */
    protected function fortify(): void
    {
        Nova::fortify()
            ->features([
                Features::updatePasswords(),
                // Features::emailVerification(),
                // Features::twoFactorAuthentication(['confirm' => true, 'confirmPassword' => true]),
            ])
            ->register();
    }

    /**
     * Register the Nova routes.
     */
    protected function routes(): void
    {
        Nova::routes()
            ->withAuthenticationRoutes()
            ->withPasswordResetRoutes()
            ->withoutEmailVerificationRoutes()
            ->register();
    }

    /**
     * Register the Nova gate.
     *
     * This gate determines who can access Nova in non-local environments.
     */
    protected function gate(): void
    {
        Gate::define('viewNova', function (User $user) {
            return $user->role_id === RoleEnum::ADMIN->value;
        });
    }

    /**
     * Get the dashboards that should be listed in the Nova sidebar.
     *
     * @return array<int, \Laravel\Nova\Dashboard>
     */
    protected function dashboards(): array
    {
        return [
            new \App\Nova\Dashboards\Main,
            new \App\Nova\Dashboards\CompanyRegistration,
            new \App\Nova\Dashboards\AnnualReturn,
            new \App\Nova\Dashboards\NameReservation,
            new \App\Nova\Dashboards\MemberChange,
            new \App\Nova\Dashboards\DirectorChange,
            new \App\Nova\Dashboards\NameChange,
            new \App\Nova\Dashboards\AddressChange,
            new \App\Nova\Dashboards\FinancialYearEnd,
            new \App\Nova\Dashboards\DocumentRequest,
            new \App\Nova\Dashboards\AuditorChange,
            new \App\Nova\Dashboards\AccountingOfficerChange,
            new \App\Nova\Dashboards\CompanySecretaryChange,
            new \App\Nova\Dashboards\AuthorisedShareChange,
            new \App\Nova\Dashboards\Checklist,
            new \App\Nova\Dashboards\BeneficialOwnershipFiling,
        ];
    }

    /**
     * Get the tools that should be listed in the Nova sidebar.
     *
     * @return array<int, \Laravel\Nova\Tool>
     */
    public function tools(): array
    {
        return [];
    }

    /**
     * Register any application services.
     */
    public function register(): void
    {
        parent::register();

        //
    }
}
