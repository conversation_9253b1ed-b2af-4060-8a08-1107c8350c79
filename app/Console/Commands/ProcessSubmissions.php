<?php

namespace App\Console\Commands;

use App\Enums\SubmissionStatusEnum;
use App\Jobs\SendToLambdasJob;
use App\Models\Submission;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Symfony\Component\Console\Command\Command as CommandAlias;

class ProcessSubmissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'submissions:process';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command sends submissions to the bots for processing.';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Processing submissions...');
        $submissions = Submission::join('endpoints', 'endpoints.id', 'submissions.endpoint_id')
            ->whereIn('status', [SubmissionStatusEnum::PENDING->value, SubmissionStatusEnum::OTP_PENDING->value])
            ->where('endpoints.enabled', 1)
            ->where(function ($query) {
                $query->whereNull('submitted_at')
                    ->orWhere('submitted_at', '<=', Carbon::now()->subMinutes(config('services.submissions.retry_wait_time')));
            })
            ->select('submissions.*')
            ->get();
        $submissions->each(function (Submission $submission) {
            $this->info("Processing submission {$submission->id}...");
            //if submission has retry_count which is greater than the retry limit, set status to manual review
            if ($submission->retry_count >= config('services.submissions.retry_limit')) {
                $submission->update([
                    'status' => SubmissionStatusEnum::MANUAL_REVIEW->value,
                ]);
                return;
            }
            $submission->update([
                'submitted_at' => Carbon::now(),
                'status' => SubmissionStatusEnum::PROCESSING->value,
                'retry_count' => $submission->retry_count + 1,
            ]);
            SendToLambdasJob::dispatch($submission);
        });
        $this->info("Processed " . $submissions->count() . " submissions.");
        return CommandAlias::SUCCESS;
    }
}
