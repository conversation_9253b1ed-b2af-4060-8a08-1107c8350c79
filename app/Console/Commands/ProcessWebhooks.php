<?php

namespace App\Console\Commands;

use App\Jobs\SendWebhookJob;
use App\Models\Webhook;
use Illuminate\Console\Command;
use Symfony\Component\Console\Command\Command as CommandAlias;
use Symfony\Component\HttpFoundation\Response;

class ProcessWebhooks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webhooks:process';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command sends webhooks.';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Processing webhooks...');
        $webhooks = Webhook::where(function ($query) {
            $query->whereNull('status_code')
                ->orWhereNotIn('status_code', [Response::HTTP_BAD_REQUEST, Response::HTTP_OK]);
        })
            ->where('retry_count', '<', config('services.submissions.webhooks_retry_limit'))
            ->get();
        $webhooks->each(function (Webhook $webhook) {
            $this->info("Processing webhook {$webhook->id}...");
            SendWebhookJob::dispatch($webhook);
        });
        return CommandAlias::SUCCESS;
    }
}
