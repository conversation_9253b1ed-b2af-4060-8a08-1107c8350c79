<?php

namespace App\Console\Commands;

use App\Enums\SubmissionStatusEnum;
use App\Models\Submission;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class ResetStuckSubmissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'submissions:reset-stuck-submissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Resets submissions that are stuck in the processing state.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("Resetting stuck submissions...");
        //submissions processing usually takes at most 15 minutes
        $stuckSubmissions = Submission::where('status', SubmissionStatusEnum::PROCESSING->value)
            ->where('updated_at', '<', Carbon::now()->subMinutes(30))
            ->get();
        $stuckSubmissions->each(function (Submission $submission) {
            if ($submission->retry_count >= config('services.submissions.retry_limit')) {
                $submission->update([
                    'status' => SubmissionStatusEnum::MANUAL_REVIEW->value,
                ]);
                return;
            }
            $submission->update([
                'status' => SubmissionStatusEnum::PENDING->value,
            ]);
        });
        $this->info("Reset " . count($stuckSubmissions) . " stuck submissions.");
    }
}
