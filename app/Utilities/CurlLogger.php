<?php

namespace App\Utilities;

class CurlLogger
{
    public function log(string $method, string $url, array|string $data = []): void
    {
        $method = strtoupper($method);
        $curlCmd = "curl -X $method ";

        if (!empty($data) && $method === 'GET') {
            $query = http_build_query($data);
            $url = $url . '?' . $query;
        }
        $curlCmd .= "'$url'";

        if ($method === 'POST') {
            $postData = json_encode($data);
            $curlCmd .= " -H 'Content-Type: application/json' -d '$postData'";
        }

        $curlCmd .= " -H 'x-api-key: " . config('services.submissions.api_key') . "'";

        // Log to a file or Laravel's logging facility
        //info($curlCmd);
    }
}
