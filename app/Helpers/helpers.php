<?php

use Carbon\CarbonImmutable;
use Carbon\CarbonInterface;

if (!function_exists('previous_range')) {
    /**
     * Calculate the previous range and calculate any short-cuts.
     *
     * @param int|string $range
     * @param string $timezone
     * @return array<int, CarbonInterface>
     */
    function previous_range(int|string $range, string $timezone = ''): array
    {
        if (!$timezone) {
            $timezone = config('app.timezone');
        }
        if ($range == 'TODAY') {
            return [
                CarbonImmutable::now($timezone)->subDay()->startOfDay(),
                CarbonImmutable::now($timezone)->subDay()->endOfDay(),
            ];
        }

        if ($range == 'YESTERDAY') {
            return [
                CarbonImmutable::now($timezone)->subDays(2)->startOfDay(),
                CarbonImmutable::now($timezone)->subDays(2)->endOfDay(),
            ];
        }

        if ($range == 'THIS_WEEK') {
            return [
                CarbonImmutable::now($timezone)->subWeek()->startOfWeek(),
                CarbonImmutable::now($timezone)->subWeek()->endOfWeek(),
            ];
        }

        if ($range == 'MTD') {
            return [
                CarbonImmutable::now($timezone)->subMonthWithoutOverflow()->startOfMonth(),
                CarbonImmutable::now($timezone)->subMonthWithoutOverflow(),
            ];
        }

        if ($range == 'QTD') {
            return previous_quarter_range($timezone);
        }

        if ($range == 'YTD') {
            return [
                CarbonImmutable::now($timezone)->subYear()->startOfYear(),
                CarbonImmutable::now($timezone)->subYear(),
            ];
        }

        return [
            CarbonImmutable::now($timezone)->subDays($range * 2),
            CarbonImmutable::now($timezone)->subDays($range)->subSecond(),
        ];
    }
}
if (!function_exists('previous_quarter_range')) {
    /**
     * Calculate the previous quarter range.
     *
     * @param string $timezone
     * @return array<int, CarbonImmutable>
     */
    function previous_quarter_range(string $timezone = ''): array
    {
        if (!$timezone) {
            $timezone = config('app.timezone');
        }
        return [
            CarbonImmutable::now($timezone)->subQuarterWithOverflow()->startOfQuarter(),
            CarbonImmutable::now($timezone)->subQuarterWithOverflow()->subSecond(),
        ];
    }
}
if (!function_exists('current_range')) {
    /**
     * Calculate the current range and calculate any short-cuts.
     *
     * @param int|string $range
     * @param string $timezone
     * @return array<int, CarbonInterface>
     */
    function current_range(int|string $range, string $timezone = ''): array
    {
        if (!$timezone) {
            $timezone = config('app.timezone');
        }
        if ($range == 'TODAY') {
            return [
                CarbonImmutable::now($timezone)->startOfDay(),
                CarbonImmutable::now($timezone)->endOfDay(),
            ];
        }

        if ($range == 'YESTERDAY') {
            return [
                CarbonImmutable::now($timezone)->subDay()->startOfDay(),
                CarbonImmutable::now($timezone)->subDay()->endOfDay(),
            ];
        }

        if ($range == 'THIS_WEEK') {
            return [
                CarbonImmutable::now($timezone)->startOfWeek(),
                CarbonImmutable::now($timezone)->endOfWeek(),
            ];
        }

        if ($range == 'MTD') {
            return [
                CarbonImmutable::now($timezone)->startOfMonth(),
                CarbonImmutable::now($timezone),
            ];
        }

        if ($range == 'QTD') {
            return current_quarter_range($timezone);
        }

        if ($range == 'YTD') {
            return [
                CarbonImmutable::now($timezone)->startOfYear(),
                CarbonImmutable::now($timezone),
            ];
        }

        return [
            CarbonImmutable::now($timezone)->subDays($range),
            CarbonImmutable::now($timezone),
        ];
    }
}
if (!function_exists('current_quarter_range')) {
    /**
     * Calculate the previous quarter range.
     *
     * @param string $timezone
     * @return array<int, CarbonImmutable>
     */
    function current_quarter_range(string $timezone = ''): array
    {
        if (!$timezone) {
            $timezone = config('app.timezone');
        }
        return [
            CarbonImmutable::now($timezone)->startOfQuarter(),
            CarbonImmutable::now($timezone),
        ];
    }
}
